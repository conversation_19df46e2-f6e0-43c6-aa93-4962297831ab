import {
  PromoCodeService,
  type IPromoCodeModel,
} from "@modules/services/promocode";
import { ref } from "vue";
import { useTracker, TrackerEvent } from "@/composable";

export class AttachPromoCode {
  private readonly promoCode: string;
  private readonly response: {
    status?: boolean;
    msg?: string;
  };

  constructor(promoCode: string) {
    this.promoCode = promoCode;
    this.response = {
      status: false,
      msg: "Server Error",
    };
  }

  private checkPromoCodeInAttachList(
    list: IPromoCodeModel[]
  ): IPromoCodeModel | undefined {
    return list.find((item) => item.code === this.promoCode);
  }

  async attach(): Promise<{ status?: boolean; msg?: string }> {
    const tracker = useTracker();

    const attachList = await PromoCodeService.list();
    if (!attachList.status) {
      this.response.status = true;
      return this.response;
    }
    const isAttachedInList = !!this.checkPromoCodeInAttachList(
      attachList.data as IPromoCodeModel[]
    );

    if (isAttachedInList) {
      this.response.status = true;
      this.response.msg = "Promo code is already attached";
      return this.response;
    }

    const promoCodeAttach = await PromoCodeService.attach({
      code: this.promoCode,
    });
    if (!promoCodeAttach.status) {
      this.response.status = false;
      return this.response;
    }

    await tracker.logEvent(TrackerEvent.PROMOCODE_ACTIVATE, {
      code: this.promoCode,
    });

    await tracker.setUserProperty(TrackerEvent.PROMOCODE_USED, this.promoCode);
    this.response.msg = "Promo code is success attached";
    this.response.status = true;
    return this.response;
  }
}

export interface IPromoCode extends IPromoCodeModel {
  value?: string;
  discount?: string;
  active?: boolean;
}

export class CheckPromoCode {
  errorStatus = ref<boolean>(false);
  errorMsg = ref<string>("Server error");
  checkPromoCodeLoading = ref<boolean>(false);
  promoCodeLocalList = ref<IPromoCode[]>([]);
  private promoCodeAttachedList = ref<IPromoCodeModel[]>([]);
  promoCodeInput = ref("");
  activePromoCode = ref("");
  activePromoCodeData = ref<IPromoCodeModel>();
  setPromoCodeLocalList(list: IPromoCode[]) {
    this.promoCodeLocalList.value = list;
  }

  clearErrors() {
    this.errorMsg.value = "Server error";
    this.errorStatus.value = false;
  }

  checkPromoCodeInLocalList(promoCode: string): boolean {
    return !!this.promoCodeLocalList.value.find(
      (item) => item.value === promoCode
    );
  }

  checkPromoCodeInLAttachList(promoCode: string): IPromoCodeModel | undefined {
    return this.promoCodeAttachedList.value.find(
      (item) => item.code === promoCode
    );
  }

  addPromoCodeToLocalList(promoCode: IPromoCodeModel) {
    if (this.checkPromoCodeInLocalList(promoCode.code)) {
      this.promoCodeInput.value = "";
      return;
    }
    this.promoCodeLocalList.value.unshift({
      value: promoCode.code!,
      discount: promoCode.fields?.card_buy_discount_percent,
      active: true,
      code: promoCode.code,
      created_at: promoCode.created_at,
      fields: promoCode.fields,
      id: promoCode.id,
      status: promoCode.status,
      stop_at: promoCode.stop_at,
    });
    this.promoCodeLocalList.value.forEach(
      (item) => (item.active = item.value === promoCode.code)
    );
    this.promoCodeInput.value = "";
    this.activePromoCode.value = promoCode.code;
    this.activePromoCodeData.value = promoCode;
    this.clearErrors();
  }

  async getAttachPromoCodeList() {
    const res = await PromoCodeService.list();
    if (!res.status) {
      this.promoCodeInput.value = "";
      this.errorStatus.value = true;
      return;
    }
    res.data = this.promoCodeAttachedList.value;
  }

  async checkPromoCode(promoCode: string) {
    this.checkPromoCodeLoading.value = true;
    await this.getAttachPromoCodeList();
    const promoCodeInAttachList = this.checkPromoCodeInLAttachList(promoCode);
    if (promoCodeInAttachList && promoCodeInAttachList.status === 0) {
      this.addPromoCodeToLocalList(promoCodeInAttachList);
      return;
    }
    const res = await PromoCodeService.check({
      params: { code: promoCode },
    });
    if (!res.status) {
      this.promoCodeInput.value = "";
      this.errorStatus.value = true;
      this.checkPromoCodeLoading.value = false;
      return;
    }
    if (!res.data?.data) {
      this.promoCodeInput.value = "";
      this.errorMsg.value = res.data?.message
        ? res.data?.message
        : "Unknown Error";
      this.errorStatus.value = true;
      this.checkPromoCodeLoading.value = false;
      return;
    }
    this.addPromoCodeToLocalList(res?.data?.data);
    this.checkPromoCodeLoading.value = false;
    this.promoCodeInput.value = "";
  }
}
