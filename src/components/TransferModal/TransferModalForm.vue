<script lang="ts" setup>
import {
  TOAST_TYPE,
  useCallToast,
  useCardParams,
  useSubscriptionsInfo,
  useTransferTransferPost,
  useTracker,
  useModalStack,
  TrackerEvent,
} from "@/composable";
import { calcAmountWithReversedCommissionAndConversion } from "@/helpers/calcAmountWithReversedCommissionAndConversion";
import { calcAmountWithCommissionAndConversion } from "@/helpers/calcAmountWithCommissionAndConversion";
import AccountsAndCardsSelect from "@/components/AccountsAndCardsSelect/AccountsAndCardsSelect.vue";
import TransferModalFormTotal from "@/components/TransferModal/TransferModalFormTotal.vue";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import CurrencyPairInputs from "@/components/CurrencyPairInputs/CurrencyPairInputs.vue";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
// import { reverseCommissionCalc } from "@/helpers/reverseCommissionCalc";
import type { TTariffResource } from "@/types/api/TTariffResource";
import { getExchangeBalance } from "@/helpers/getExchangeBalance";
import type { TCardResource } from "@/types/api/TCardResource";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIDialog from "@/components/ui/UIDialog/UIDialog.vue";
import { RouteName } from "@/constants/route_name";
import { prepareAccountBalance } from "@/helpers";
import type { TXUR } from "@/types/api/TXUR";
import { useUserStore } from "@/stores/user";
import { Skeletor } from "vue-skeletor";
import { useRouter } from "vue-router";
import BigNumber from "bignumber.js";
import { computed, ref } from "vue";
import { capitalize } from "lodash";
import type { TMemberAccountResource } from "@/types/api/TMemberAccountResource";
import type {
  FormStage,
  TransferFormAccountOption,
  TransferFormState,
} from "@/components/TransferModal/types";
import type { TDashboardService } from "@/types/api/TDashboardService";
import DiscountIndicatorTopUpCard from "@/components/DiscountIndicatorTopUpCard.vue";
import { experiments } from "@/config/cards";
import UsernameWithDeleted from "@/components/UsernameWithDeleted/UsernameWithDeleted.vue";
import TransferCompleted from "@/components/TransferCompleted/TransferCompleted.vue";
import type { IsoCodeNames } from "@/constants/iso_code_names";
import UIBlock from "@/components/ui/UIBlock/UIBlock.vue";
import { useI18nWrapper } from "@/composable/useI18nWrapper";

const MIN_BALANCE_FOR_ZERO_WITHDRAWAL = 0.1;
const MIN_BALANCE_FOR_STANDARD_WITHDRAWAL = 1.1;

const { t, formatCurrency } = useI18nWrapper();
const { closeAllModals } = useModalStack();
const router = useRouter();

const { subscriptionsStatus } = useSubscriptionsInfo();
const userStore = useUserStore();
const tracker = useTracker();

const props = defineProps<{
  exchangeRates: TXUR;
  userTariffs: TTariffResource[];
  tariffs: TTariffResource[];
  dashboardData: TDashboardService[];
  formState: TransferFormState;
  currentStage: FormStage;
  searchFrom: (value: string) => void;
  searchTo: (value: string) => void;
  loadMoreFrom: (value: boolean) => void;
  loadMoreTo: (value: boolean) => void;
  amount?: number;
}>();

const emit = defineEmits<{
  close: [];
  updateStage: [stage: FormStage];
}>();

const fromAmount = ref<number>(0);
const toAmount = ref<number>(0);

const validationObject = ref<{
  isPositiveFromAccountBalance: string | null;
  fromAmountError: string | null;
  toAmountError: string | null;
  kycLimitError: string | null;
}>({
  isPositiveFromAccountBalance: null,
  fromAmountError: "",
  toAmountError: null,
  kycLimitError: null,
});

/*
 * Amount inputs touched
 * We don't validate inputs until user touches them
 */
const amountInputsTouched = ref<boolean>(false);

const fromOptionId = ref<number | null>(props.formState.selectedFromId);

const toOptionId = ref<number | null>(props.formState.selectedToId);

const fromAccount = computed<TransferFormAccountOption | null>(() => {
  return findAccountById(
    [
      ...props.formState.fromAccountOptions,
      ...props.formState.fromCardsOptions,
    ],
    fromOptionId.value
  );
});

const toAccount = computed<TransferFormAccountOption | null>(() => {
  return findAccountById(
    [...props.formState.toAccountOptions, ...props.formState.toCardsOptions],
    toOptionId.value
  );
});

const toCard = computed<TCardResource | null>(() => {
  return (
    props.formState.toCardsOptions.find(
      (card) => card.account.id === toOptionId.value
    ) || null
  );
});

const isFacebookCardsExperiments = computed(() =>
  Boolean(
    isFacebookToCard.value &&
      (userStore.user?.experiments?.fbdeposit_discount2 ||
        userStore.user?.experiments?.fbdeposit_discount1)
  )
);

const selectedTariff = computed(() => {
  if (
    !(subscriptionsStatus.value && !toCard.value?.subscription_tariff) ||
    userStore.user.is_old_user_for_subscription
  ) {
    return (
      props.userTariffs.find(
        (tariff) => tariff.id === toCard.value?.tariff_id
      ) || null
    );
  }
  return (
    props.tariffs.find((tariff) => tariff.id === toCard.value?.tariff_id) ||
    null
  );
});

const isFacebookToCard = computed(
  () => selectedTariff.value?.slug === "facebook-cards"
);

const fromCard = computed<TCardResource | null>(() => {
  return (
    props.formState.fromCardsOptions.find(
      (card) => card.account.id === fromOptionId.value
    ) || null
  );
});

const fromCurrency = computed<IsoCodeNames | null>(() => {
  if (!fromAccount.value) return null;
  return (
    getAccountCurrencyByCurrencyId(fromAccount.value?.currency_id)?.isoCode ||
    null
  );
});

const toCurrency = computed<IsoCodeNames | null>(() => {
  if (!toOptionId.value || !toAccount.value) return null;
  return (
    getAccountCurrencyByCurrencyId(toAccount.value.currency_id)?.isoCode || null
  );
});

const conversionRate = computed<number>(() => {
  if (!fromCurrency.value || !toCurrency.value || !isDifferentCurrency.value) {
    return 1;
  }

  return Number(props.exchangeRates[fromCurrency.value][toCurrency.value]);
});

const fromChanged = ref(false);

const commissionPercent = computed<number>(() => {
  if (!toOptionId.value) return 0;

  const cardTo = props.formState.toCardsOptions.find(
    (card) => toOptionId.value === card.account.id
  );
  if (!cardTo) return 0;

  if (isFacebookCardsExperiments.value && selectedTariff.value?.fee_topup) {
    const comissionWithDiscount =
      Number(selectedTariff.value.fee_topup) -
      experiments.facebookCards.discountFeeTopUpPercent;
    const minDiscountSumWithFee = calcAmountWithReversedCommissionAndConversion(
      experiments.facebookCards.discountFeeTopUpFromUsd,
      conversionRate.value,
      comissionWithDiscount
    );
    if (
      (fromChanged.value && fromAmount.value >= minDiscountSumWithFee) ||
      (!fromChanged.value &&
        toAmount.value >= experiments.facebookCards.discountFeeTopUpFromUsd)
    ) {
      return comissionWithDiscount;
    }
  }

  return Number(selectedTariff.value?.fee_topup) || 0;
});

const isDifferentCurrency = computed<boolean>(() => {
  if (fromCurrency.value !== null && toCurrency.value !== null) {
    return fromCurrency.value !== toCurrency.value;
  } else {
    return false;
  }
});

const exchangeRatesText = computed<string | null>(() => {
  if (!isDifferentCurrency.value || !fromCurrency.value || !toCurrency.value)
    return null;

  if (
    (fromCurrency.value === "USD" && toCurrency.value === "USDT") ||
    (fromCurrency.value === "USDT" && toCurrency.value === "USD")
  ) {
    return `${formatCurrency(1, fromCurrency.value)} = ${formatCurrency(
      Number(conversionRate.value),
      toCurrency.value
    )}`;
  } else {
    const exchangeBalanceWithSymbol = getExchangeBalance(1, {
      inputIsoCode: fromCurrency.value,
      outputIsoCode: toCurrency.value,
      rates: props.exchangeRates,
    }).exchangeBalanceWithSymbol;
    return `${formatCurrency(
      1,
      fromCurrency.value
    )} = ${exchangeBalanceWithSymbol}`;
  }
});

const isSendStage = computed<boolean>(() => props.currentStage === "send");

const cardDepositLimit = computed<number>(() => {
  return Number(userStore.actualNum.deposit) > 0
    ? Number(userStore.actualNum.deposit)
    : 0;
});

const fromDirection = computed<"card" | "account" | null>(() => {
  switch (fromAccount.value?.type) {
    case 0:
      return "account";
    case 2:
      return "card";
    default:
      return null;
  }
});

const toDirection = computed<"card" | "account" | null>(() => {
  switch (toAccount.value?.type) {
    case 0:
      return "account";
    case 2:
      return "card";
    default:
      return null;
  }
});

const fromAmountMax = computed<number>(() => {
  if (fromDirection.value === "card") {
    const fromCardAccountBalance = fromCard.value?.is_zero_withdrawal_available
      ? Number(fromAccount.value?.balance)
      : Number(fromAccount.value?.balance) - 1;

    return fromCardAccountBalance > 0 ? fromCardAccountBalance : 0;
  }
  return Number(fromAccount.value?.balance) || 0;
});

const enableContinueButton = computed<boolean>(() => {
  // all values in validationObject must be null and toOptionId and fromOptionId must be not null
  return (
    Object.values(validationObject.value).every((value) => value === null) &&
    !!toOptionId.value &&
    !!fromOptionId.value
  );
});

const toAccountOptions = computed<TransferFormAccountOption[]>(() => {
  return (
    props.formState.toAccountOptions.filter(
      (account) => account.id !== fromOptionId.value
    ) ?? []
  );
});

const toCardOptions = computed<TCardResource[]>(() => {
  return (
    props.formState.toCardsOptions.filter(
      (card) => card.account.id !== fromOptionId.value
    ) ?? []
  );
});

const totals = computed(() => {
  const toAmountNumber = prepareAccountBalance(
    toAmount.value,
    toCurrency.value || "USD"
  );
  const fromAmountNumber = prepareAccountBalance(
    fromAmount.value,
    fromCurrency.value || "USD"
  );
  return {
    fomAmount: formatCurrency(
      Number(fromAmountNumber),
      fromCurrency.value ?? undefined
    ),
    toAmount: formatCurrency(
      Number(toAmountNumber),
      toCurrency.value ?? undefined
    ),
  };
});

// const tariffsForExcludeDisplayPromoBanner = [
//   "ultima-annually-3ds",
//   "ultima-weekly-3ds",
//   "ultima-3ds",
//   "ultima-annually",
//   "ultima-semiannually",
//   "ultima-quarterly",
//   "ultima-weekly",
//   "ultima",
//   "sigma_no_limits",
//   "pst-black-prem",
//   "exclusive",
//   "pst-black",
//   "3ds",
//   "platinum-credit",
//   "fb-prem",
//   "adv",
//   "pst-black-uniq-bin",
// ];

// const fromCurrencySymbol = computed<TIsoSymbol | null>(() =>
//   fromCurrency.value ? getCurrencySymbolByIso(fromCurrency.value) : null
// );

// const privateSaveMoneyTopUpFee = computed<string | number>(() => {
//   return prepareAccountBalance(
//     feeAmount.value,
//     String(fromCurrency.value),
//     true
//   );
// });

// const canShowPrivateSaveMoneyBanner = computed<boolean>(() => {
//   const toCard = toCardOptions.value.find(
//     (card) => card.account.id === toOptionId.value
//   );
//   if (!toCard) return false;
//
//   const tariffSlug =
//     props.userTariffs.find((tariff) => tariff.id === toCard?.tariff_id)?.slug ||
//     "";
//
//   return (
//     props.currentStage === "send" &&
//     commissionPercent.value > PrivateTariffValues.feeTopUpPercent &&
//     !subscriptionsStatus.value &&
//     !userStore.user?.is_old_user_for_subscription &&
//     !userStore.isTeamMember &&
//     !tariffsForExcludeDisplayPromoBanner.includes(tariffSlug) &&
//     toCard?.tariff_id > 10
//   );
// });

// const feeAmount = computed<number>(() => {
//   return reverseCommissionCalc(fromAmount.value, commissionPercent.value)
//     .commission;
// });

// for transfer to card minimal amount is 1 USD
const minCommissionAmount = computed(() => {
  return toDirection.value === "card" && toCurrency.value === "USD" ? 1 : 0;
});

const { cardMinimumBalanceForBlock } = useCardParams(toCard);

/*
 * To calculate minimum amount we need to validate against several rules
 */
const minFromAmount = computed<number>(() => {
  // collecting minimum amounts so we can show the largest one
  const minFromAmountsArray: number[] = [];

  /*
   * "to" card should be positive at least 1 USD
   */

  if (
    toDirection.value === "card" &&
    Number(toAccount.value?.balance_default) < 1
  ) {
    const differenceToZero = Math.abs(Number(toAccount.value?.balance_default));

    // we want end up with at least 1 USD positive balance if is_zero_withdrawal_available = false
    const minFromAmountForNegativeCard =
      differenceToZero + cardMinimumBalanceForBlock.value;

    const minAmount = calcAmountWithReversedCommissionAndConversion(
      minFromAmountForNegativeCard,
      conversionRate.value,
      commissionPercent.value,
      minCommissionAmount.value
    );

    const minAmountPrepared = prepareAccountBalance(
      minAmount,
      fromCurrency.value || "USD",
      false,
      BigNumber.ROUND_UP
    );
    minFromAmountsArray.push(Number(minAmountPrepared));
  }

  /*
   * minimum amount that can be transferred for USDT, USD, EUR = 0.1
   */
  if (
    fromCurrency.value &&
    ["USDT", "USD", "EUR"].includes(fromCurrency.value)
  ) {
    const minAmount = calcAmountWithReversedCommissionAndConversion(
      0.1,
      1,
      commissionPercent.value,
      minCommissionAmount.value
    );

    const minAmountPrepared = prepareAccountBalance(
      minAmount,
      fromCurrency.value || "USD",
      false,
      BigNumber.ROUND_UP
    );
    minFromAmountsArray.push(Number(minAmountPrepared));
  }

  /*
   * For currency BTC, minimum amount to be transferred is 0.1$, 0.1 Eur, 0.1 USDT by exchange rate.
   */

  if (fromCurrency.value === "BTC" && toCurrency.value !== "BTC") {
    const minAmount = calcAmountWithReversedCommissionAndConversion(
      0.1,
      conversionRate.value,
      commissionPercent.value,
      minCommissionAmount.value
    );

    const minAmountPrepared = prepareAccountBalance(
      minAmount,
      fromCurrency.value || "USD",
      false,
      BigNumber.ROUND_UP
    );
    minFromAmountsArray.push(Number(minAmountPrepared));
  }

  /*
   * for BTC -> BTC transfers, minimum amount to be transferred is 0.********
   */
  if (fromCurrency.value === "BTC" && toCurrency.value === "BTC") {
    const minAmountPrepared = prepareAccountBalance(
      0.********,
      fromCurrency.value || "USD",
      false,
      BigNumber.ROUND_UP
    );
    minFromAmountsArray.push(Number(minAmountPrepared));
  }

  /*
   * Minimum amount to own account or card is 450$ for users with show_warn = true
   */
  const isSoloUser = !userStore.isTeamMember && !userStore.isTeamOwner;
  if (userStore.user?.show_warn && isSoloUser) {
    const toUsdExchangeRate =
      !fromCurrency.value || fromCurrency.value === "USD"
        ? 1
        : Number(props.exchangeRates[fromCurrency.value]["USD"]);
    const minAmount = calcAmountWithReversedCommissionAndConversion(
      450,
      toUsdExchangeRate,
      commissionPercent.value,
      minCommissionAmount.value
    );
    const minAmountPrepared = prepareAccountBalance(
      minAmount,
      fromCurrency.value || "USD",
      false,
      BigNumber.ROUND_UP
    );
    minFromAmountsArray.push(Number(minAmountPrepared));
  }

  // we have multiple minimum amounts, we want to show the largest one
  return Math.max(...minFromAmountsArray, 0);
});

const showSwapFromAndTo = computed(() => {
  const toOptionsContainFromOption = props.formState.toAccountOptions.some(
    (entity) => entity.id === fromOptionId.value
  );
  return (
    !props.formState.fromReadonly &&
    !props.formState.toReadonly &&
    fromAccount.value &&
    toAccount.value &&
    toOptionsContainFromOption &&
    props.currentStage === "calculate"
  );
});

const showMaxButton = computed<boolean>(() => {
  return (
    Number(fromAccount.value?.balance) !== 0 &&
    fromOptionId.value !== null &&
    toOptionId.value !== null &&
    fromAmount.value !== fromAmountMax.value
  );
});

const fromUser = computed<string>(() => {
  const identity =
    (isMemberCard(fromCard.value) &&
      (fromCard.value?.user_name || fromCard.value?.user_email)) ||
    (isMemberAccount(fromAccount.value) &&
      (fromAccount.value.user_name || fromAccount.value.user_email));

  return identity ? `${identity}` : "";
});

const toUser = computed<string>(() => {
  const memberIdentity =
    (isMemberCard(toCard.value) &&
      (toCard.value?.user_name || toCard.value?.user_email)) ||
    (isMemberAccount(toAccount.value) &&
      (toAccount.value.user_name || toAccount.value.user_email));

  if (memberIdentity) {
    return memberIdentity;
  }

  if (isMemberCard(fromCard.value) || isMemberAccount(fromAccount.value)) {
    return t("transfer-modal.from-member-to-master-hint");
  }

  return "";
});

const changeFromOptionHandle = (value: any) => {
  if ((isCard(value) ? value.account?.id : value.id) === toOptionId.value) {
    toOptionId.value = null;
  }

  fromOptionId.value = isCard(value) ? value.account.id : value.id;

  if (toDirection.value === "card") {
    handleFromAmountInput(minFromAmount.value);
  } else {
    handleFromAmountInput(0);
  }
  validateFromAndToAccounts();
};

const changeToOptionHandle = (value: any) => {
  toOptionId.value = value.account?.id ?? value.id;

  if (!fromOptionId.value) {
    return;
  }

  if (toDirection.value === "card") {
    handleFromAmountInput(minFromAmount.value);
  } else {
    handleFromAmountInput(0);
  }
  validateFromAndToAccounts();
};

const handleFromAmountInput = (v: number | null) => {
  amountInputsTouched.value ||= true;
  fromChanged.value = true;

  fromAmount.value = Number(v);
  if (!toCurrency.value) return;

  if (fromAmount.value === 0) {
    toAmount.value = 0;
  } else {
    const calculatedAmount = calcAmountWithCommissionAndConversion(
      fromAmount.value,
      conversionRate.value,
      commissionPercent.value,
      minCommissionAmount.value
    );
    toAmount.value = Number(
      prepareAccountBalance(
        calculatedAmount,
        toCurrency.value,
        false,
        BigNumber.ROUND_DOWN
      )
    );
  }
};

const handleToAmountInput = (v: number | null) => {
  amountInputsTouched.value ||= true;
  fromChanged.value = false;

  if (!fromCurrency.value) return;

  toAmount.value = Number(v);
  if (toAmount.value === 0) {
    fromAmount.value = 0;
  } else {
    const calculatedAmount = calcAmountWithReversedCommissionAndConversion(
      toAmount.value,
      conversionRate.value,
      commissionPercent.value,
      minCommissionAmount.value
    );
    fromAmount.value = Number(
      prepareAccountBalance(
        calculatedAmount,
        fromCurrency.value,
        false,
        BigNumber.ROUND_UP
      )
    );
  }
  if (isFacebookCardsExperiments.value && selectedTariff.value?.fee_topup) {
    handleFromAmountInput(fromAmount.value);
  }
};

const handleInputBlur = () => {
  amountInputsTouched.value ||= true;
  validateForm();
};

const passVerification = () => {
  emit("close");
  router.push({ name: RouteName.VERIFICATION_SETTINGS });
};

const clearValidationErrors = () => {
  Object.keys(validationObject.value).forEach((key) => {
    validationObject.value[key as keyof typeof validationObject.value] = null;
  });
};

const validateFromAndToAccounts = () => {
  validationObject.value.isPositiveFromAccountBalance = null;

  // Balance must be positive
  if (Number(fromAccount.value?.balance) <= 0) {
    validationObject.value.isPositiveFromAccountBalance = t(
      "transfer-modal.errors.balanceMustBePositive"
    );
  }

  // KYC limit - to card
  const isDirectionToCard = !!findAccountById(
    props.formState.toCardsOptions,
    Number(toOptionId.value)
  );

  const isOverKycLimit =
    ["welcome", "scale"].includes(userStore.actual) &&
    parseFloat(Number(toAmount.value).toFixed(2)) > cardDepositLimit.value;

  if (isDirectionToCard && isOverKycLimit) {
    validationObject.value.kycLimitError = t(
      "transfer-modal.error.text.kyc_limit",
      {
        kyc_actual: capitalize(userStore.actual),
        kyc_deposit_limit:
          userStore.userActualLimit?.userLimit.card_deposit_limit,
      }
    );
  }
};

const validateForm = () => {
  clearValidationErrors();

  validateFromAndToAccounts();

  // If amount inputs are not touched, don't validate
  if (!amountInputsTouched.value) return;

  /*
   * Transfer amount must be less than account balance
   * check if "from" amount is greater than "from" balance
   */
  if (fromAmount.value > fromAmountMax.value) {
    validationObject.value.fromAmountError = t(
      "transfer-modal.errors.notEnoughFunds"
    );
  }

  /*
   * "From" amount must be greater than 0
   */
  if (fromAmount.value <= 0) {
    validationObject.value.fromAmountError = t(
      "transfer-modal.errors.zeroAmount"
    );
  }

  /*
   * "To" amount must be greater than 0
   */
  if (toAmount.value <= 0) {
    validationObject.value.toAmountError = t(
      "transfer-modal.errors.zeroAmount"
    );
  }

  if (fromAmount.value < minFromAmount.value) {
    validationObject.value.fromAmountError = t(
      "transfer-modal.errors.minimumAmount",
      {
        a: BigNumber(minFromAmount.value).toFixed(),
        c: fromCurrency.value,
      }
    );
  }
};

const handleMaxClick = () => {
  handleFromAmountInput(fromAmountMax.value);
  validateForm();
};

function isCard(
  entity: TCardResource | TransferFormAccountOption
): entity is TCardResource {
  return "account" in entity;
}

const isMemberCard = (card: TCardResource | null): boolean => {
  if (!card || (!userStore.isTeamOwner && !userStore.isTeamMember))
    return false;
  return userStore.user.email !== card.user_email;
};

function isMemberAccount(
  entity: TransferFormAccountOption | null
): entity is TMemberAccountResource {
  return entity !== null && "user_id" in entity;
}

function findAccountById(
  entities: (TCardResource | TransferFormAccountOption)[],
  id: TUserAccountResource["id"] | null
): TransferFormAccountOption | null {
  if (!id) return null;
  for (const entity of entities) {
    if (isCard(entity) && entity.account.id === id) {
      return entity.account;
    } else if (!isCard(entity) && entity.id === id) {
      return entity;
    }
  }
  return null;
}

const handleTransferRequest = async () => {
  try {
    updateCurrentStage("transfer-in-progress");

    const fetchReturn = await useTransferTransferPost({
      from_account_id: fromAccount.value?.id || 0,
      to_account_iban: toAccount.value?.iban,
      amount: String(
        prepareAccountBalance(fromAmount.value, String(fromCurrency?.value))
      ),
      with_commission: "1",
    });
    userStore.updateActualKycLevel();

    if (fetchReturn.response.value?.ok) {
      updateCurrentStage("transfer-completed");
    } else {
      updateCurrentStage("calculate");
      // check kyc limit error
      if (
        fetchReturn.data?.value?.errors?.code === "kyc_limit" ||
        fetchReturn.data?.value?.error?.code === "kyc_limit"
      ) {
        validationObject.value.kycLimitError = t(
          "transfer-modal.error.text.kyc_limit",
          {
            kyc_actual: capitalize(userStore.actual),
            kyc_deposit_limit:
              userStore.userActualLimit?.userLimit.card_deposit_limit,
          }
        );
      } else {
        useCallToast({
          title: t("toast.error"),
          body: t(
            `errors.${fetchReturn.data.value?.error?.code}`,
            t("errors.universal-transfer-request-error")
          ),
          options: {
            type: TOAST_TYPE.ERROR,
            timeout: 3000,
          },
        });
      }
    }

    tracker.logEvent(TrackerEvent.CARD_TOP_UP);
  } catch (ex) {
    updateCurrentStage("calculate");
  }
};

const updateCurrentStage = (stage: FormStage) => {
  emit("updateStage", stage);
};

const closeKycLimitModalHandle = () => {
  handleToAmountInput(cardDepositLimit.value);
  validationObject.value.kycLimitError = null;
};

const swapFromAndTo = () => {
  if (!showSwapFromAndTo.value) return;
  [fromOptionId.value, toOptionId.value] = [
    toOptionId.value,
    fromOptionId.value,
  ];
};

const handleCurrencyInput = (value: number | null, side: "left" | "right") => {
  // do not show errors while user is typing
  validationObject.value.fromAmountError = null;
  validationObject.value.toAmountError = null;

  if (side === "left") {
    handleFromAmountInput(value);
  } else {
    handleToAmountInput(value);
  }
};

const cardsFilteredByBalance = computed(() => {
  return props.formState.fromCardsOptions.filter((item) => {
    if (item.is_zero_withdrawal_available) {
      return Number(item.account.balance) > MIN_BALANCE_FOR_ZERO_WITHDRAWAL;
    } else {
      return Number(item.account.balance) > MIN_BALANCE_FOR_STANDARD_WITHDRAWAL;
    }
  });
});

// set amount from prop
if (props.amount) {
  handleFromAmountInput(props.amount);
}

const redirectToSubscriptionPage = () => {
  closeAllModals();
  router.push({ name: RouteName.SUBSCRIPTION });
};
</script>

<template>
  <div class="h-full">
    <div
      v-if="currentStage === 'send' || currentStage === 'calculate'"
      class="flex flex-col">
      <p
        v-if="currentStage === 'send'"
        class="text-5 font-medium">
        {{ $t("transfer-modal.confirm-transaction") }}
      </p>

      <AccountsAndCardsSelect
        v-if="exchangeRates"
        :accounts="formState.fromAccountOptions"
        :cards="cardsFilteredByBalance"
        :error="validationObject.isPositiveFromAccountBalance ?? ''"
        :label="fromUser"
        :load-more="loadMoreFrom"
        :model-value="fromOptionId"
        :rates="exchangeRates"
        :readonly="isSendStage || formState.fromReadonly"
        :search-handler="props.searchFrom"
        :searchable="props.formState.fromCardsSearchable"
        :tariffs="userTariffs"
        @change="changeFromOptionHandle">
        <template #label="labelProps">
          <UsernameWithDeleted
            :class="[labelProps.error ? 'text-fg-red' : 'text-fg-secondary']"
            :prefix="`${t('transfer-modal.from-select-label')}&nbsp;`"
            :username="fromUser ? `(${fromUser})` : ''"
            class="mb-1"
            icon-class="text-inherit" />
        </template>
      </AccountsAndCardsSelect>

      <div class="flex justify-center items-center my-3 py-2.5">
        <UIButton
          v-if="showSwapFromAndTo"
          color="grey-free"
          @click="swapFromAndTo">
          <DynamicIcon
            class="w-6 h-6 rotate-90"
            name="exchange-alt" />
        </UIButton>
      </div>
      <AccountsAndCardsSelect
        v-if="exchangeRates"
        :accounts="toAccountOptions"
        :cards="toCardOptions"
        :label="toUser"
        :load-more="loadMoreTo"
        :model-value="toOptionId"
        :rates="exchangeRates"
        :readonly="isSendStage || formState.toReadonly"
        :search-handler="props.searchTo"
        :searchable="props.formState.toCardsSearchable"
        :tariffs="userTariffs"
        class="mb-10"
        @change="changeToOptionHandle">
        <template #label>
          <UsernameWithDeleted
            :prefix="`${t('transfer-modal.to-select-label')}&nbsp;`"
            :username="toUser ? `(${toUser})` : ''"
            class="mb-1 text-fg-secondary" />
        </template>
      </AccountsAndCardsSelect>
      <div class="mb-10">
        <CurrencyPairInputs
          v-show="!isSendStage"
          :left-amount="fromAmount"
          :right-amount="toAmount"
          :left-currency="fromCurrency"
          :left-error="validationObject.fromAmountError"
          :left-label="$t('transfer-modal.amount-label')"
          :right-currency="toCurrency"
          :right-error="validationObject.toAmountError"
          :right-label="$t('transfer-modal.amount-enrollment-label')"
          @blur="handleInputBlur"
          @key-up-enter="validateForm"
          @input="handleCurrencyInput">
          <template
            v-if="showMaxButton"
            #leftAction>
            <button
              class="font-medium text-lg leading-none text-fg-blue"
              @click="handleMaxClick">
              {{ $t("transfer-modal.max") }}
            </button>
          </template>
        </CurrencyPairInputs>

        <div class="flex flex-col mt-2 gap-10">
          <TransferModalFormTotal
            v-if="!isFacebookCardsExperiments || exchangeRatesText"
            :amount-credited="totals.toAmount"
            :transfer-amount="totals.fomAmount"
            :commission-percent="commissionPercent"
            :exchange-rates-text="exchangeRatesText"
            :is-send-stage="isSendStage"
            :show-min-commission="!isFacebookCardsExperiments" />

          <DiscountIndicatorTopUpCard
            v-if="isFacebookCardsExperiments"
            :amount="toAmount"
            :discount-from="experiments.facebookCards.discountFeeTopUpFromUsd"
            :fee-discount="experiments.facebookCards.discountFeeTopUpPercent"
            :fee-top-up="Number(selectedTariff?.fee_topup || '0')" />
        </div>
      </div>

      <template v-if="currentStage === 'calculate'">
        <UIBlock
          v-if="!userStore.isTeamMember && !subscriptionsStatus && fromCard"
          class="mb-10">
          <template #title>{{
            $t("transfer-modal.card-information-block.title")
          }}</template>
          <template #content>
            <div class="flex flex-col gap-4">
              {{ $t("transfer-modal.card-information-block.subtitle") }}

              <UIButton
                color="orange-solid"
                class="w-full"
                @click="redirectToSubscriptionPage">
                {{ $t("transfer-modal.card-information-block.button") }}
                Private
              </UIButton>
            </div>
          </template>
        </UIBlock>

        <UIButton
          :disabled="!enableContinueButton"
          color="black"
          @click="updateCurrentStage('send')">
          {{ $t("transfer-modal.continue-btn") }}
        </UIButton>
      </template>

      <UIButton
        v-if="currentStage === 'send'"
        class="mb-10"
        color="black"
        @click="handleTransferRequest">
        {{ $t("transfer-modal.send-btn") }}
      </UIButton>
      <!--      <template v-if="canShowPrivateSaveMoneyBanner">-->
      <!--        <SaveMoneyPrivateTransfer-->
      <!--          :amount="Number(fromAmount)"-->
      <!--          :fee-currency="String(fromCurrency)"-->
      <!--          :fee-currency-symbol="String(fromCurrencySymbol)"-->
      <!--          :top-up-fee="privateSaveMoneyTopUpFee"-->
      <!--          :top-up-fee-percent="commissionPercent || undefined" />-->
      <!--      </template>-->
    </div>
    <TransferCompleted
      v-if="currentStage === 'transfer-completed'"
      :to-direction="toDirection"
      :from-direction="fromDirection"
      :to-card="toCard"
      :has-private="subscriptionsStatus"
      :to-amount="toAmount"
      :to-currency="toCurrency"
      :to-account="toAccount"
      :from-account="fromAccount"
      :from-currency="fromCurrency"
      :from-card="fromCard"
      @close="emit('close')">
      <template
        v-if="toUser"
        #to>
        {{ toUser }}
      </template>
    </TransferCompleted>
    <div
      v-if="currentStage === 'transfer-in-progress'"
      class="flex flex-col justify-center items-center h-full">
      <Skeletor
        circle
        size="56" />
      <div class="text-center mt-10 h-11 w-full">
        <Skeletor width="90%" />
      </div>
      <div class="flex justify-center mt-10 h-11 w-full">
        <Skeletor
          height="50"
          width="40%" />
      </div>
    </div>
    <UIDialog
      :btn-confirm-text="t('transfer-modal.passVerification')"
      :callback-confirm="passVerification"
      :callback-cancel="closeKycLimitModalHandle"
      :is-open="!!validationObject.kycLimitError"
      show-btn-cancel
      with-close-icon
      :btn-cancel-text="t('transfer-modal.close')"
      :text="String(validationObject.kycLimitError)"
      :title="t('transfer-modal.accountNotVerified')"
      @close="closeKycLimitModalHandle">
    </UIDialog>
  </div>
</template>
