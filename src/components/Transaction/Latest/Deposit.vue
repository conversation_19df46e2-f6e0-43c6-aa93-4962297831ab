<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { computed, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import WebPushStatic from "@/components/Banners/WebPush/WebPushStatic.vue";
import DepositGatewaySelect from "@/components/DepositGatewaySelect.vue";
import TransactionMessage from "@/components/Transaction/Latest/Message.vue";
import DepositUpdatedTermsApproval from "@/components/Transaction/Latest/Deposit/DepositUpdatedTermsApproval.vue";
import DepositByCrypto from "@/components/Transaction/Latest/Deposit/DepositByCrypto.vue";
import DepositByAnyCrypto from "@/components/Transaction/Latest/Deposit/DepositByAnyCrypto.vue";
import DepositByCard from "@/components/Transaction/Latest/Deposit/DepositByCard.vue";
import DepositByOtherGateway from "@/components/Transaction/Latest/Deposit/DepositByOtherGateway.vue";
import { useDeposit } from "@/composable/Transaction";
import { useUserIsWarn, useTracker, TrackerEvent } from "@/composable";
import type {
  TDepositProps,
  TDepositToDirection,
} from "@/components/Transaction/Latest/types";
import { LocalStorageKey } from "@/constants/local_storage_key";

const props = defineProps<TDepositProps>();
const emit = defineEmits([
  "on-change-to-id",
  "on-change-num-step",
  "on-change-step",
  "on-close",
  "on-submit",
]);
const {
  state,
  allDepositGateway,
  activeAccount,
  usdtAccount,
  activeCrypto,
  setState,
  setGateway,
  replaceQuery,
} = useDeposit();

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const tracker = useTracker();
const { userIsWarnEffect } = useUserIsWarn();
const errorMsg = ref<string | undefined>();
const isApprovedUpdatedTerms = ref<boolean>(false);

/** Only for users with 'show_warn = true' */
const showUserIsWarnStep = computed<boolean>(() => {
  if (!userIsWarnEffect.value) return false;

  const isApprovedStorage = localStorage.getItem(
    LocalStorageKey.USER_WARN_TERMS_APPROVED
  );
  return !isApprovedUpdatedTerms.value && !isApprovedStorage;
});

const onApproveUpdatedTerms = () => {
  isApprovedUpdatedTerms.value = true;
  localStorage.setItem(LocalStorageKey.USER_WARN_TERMS_APPROVED, "true");
};

const onSetGateway = (id: number | string, direction: TDepositToDirection) => {
  errorMsg.value = "";
  setGateway(id, direction);
  emit("on-change-to-id", id);

  if (direction === "crypto") {
    emit("on-change-num-step", 2);
    emit("on-change-step", 1);
  } else emit("on-change-num-step", 1);
};

const onSubmit = (success: boolean, error?: string) => {
  errorMsg.value = t(
    `errors.exchange.${error?.toLowerCase().replace(/ /g, "_")}`
  );
  emit("on-submit", success, error);
};

onMounted(() => {
  setState(props);
});

watch(
  props,
  (newProps) => {
    setState(newProps);
  },
  { immediate: true, deep: true }
);

watch(
  [state, () => route.query?.action],
  ([newState]) => {
    if (newState) replaceQuery(route, router);
  },
  { deep: true, immediate: true }
);

if (!showUserIsWarnStep.value) {
  tracker.logEvent(TrackerEvent.DEPOSIT_SHOW_QR);
}
</script>

<template>
  <Transition
    name="fade-slide-down"
    mode="out-in">
    <DepositUpdatedTermsApproval
      v-if="showUserIsWarnStep"
      @submit="onApproveUpdatedTerms" />

    <div
      v-else
      :class="deposit.root">
      <div
        v-if="props.step === 1"
        :class="deposit.block">
        <DepositGatewaySelect
          v-if="allDepositGateway"
          :error="errorMsg"
          :is-loading="state.toId === -1 || state.loading"
          :label="t('label.paymentMethods')"
          :model-value="state.toId"
          :options="allDepositGateway"
          searchable
          size="l"
          @change="(id, direction) => onSetGateway(id, direction)" />
      </div>
      <template v-if="state.toDirection === 'account'">
        <div :class="deposit.block">
          <DepositByCrypto
            v-if="activeAccount"
            :account="activeAccount"
            @on-submit="(success, error) => onSubmit(success, error)" />
        </div>
      </template>

      <template v-else-if="state.toDirection === 'crypto'">
        <DepositByAnyCrypto
          :gateway="activeCrypto"
          :step="props.step"
          :usdt-address="usdtAccount?.addresses[0]?.address || ''"
          @on-submit="(success, error) => onSubmit(success, error)"
          @on-change-step="(v) => emit('on-change-step', v)"
          @on-close="emit('on-close')" />
      </template>

      <template
        v-else-if="
          state.toDirection === 'external' &&
          ['mastercard', 'visa'].includes(String(state.toId))
        ">
        <div class="flex flex-col gap-4">
          <TransactionMessage
            :class="deposit.message"
            :text="t('deposit.visa.infoText')"
            :title="t('label.information')" />
          <TransactionMessage
            v-if="userIsWarnEffect"
            type="alert"
            :title="t('transaction.depositWarning.attention')"
            :text="t('transaction.depositWarning.attention_text_warn')" />
        </div>
        <DepositByCard />
      </template>

      <template v-else>
        <DepositByOtherGateway />
      </template>

      <WebPushStatic
        :img-size="66"
        subtitle="common.web-push-static.top-up-notify" />
    </div>
  </Transition>
</template>

<style lang="scss" module="deposit">
.root {
  @apply flex flex-col gap-10 h-full;
}

.inputs {
  @apply grid grid-cols-2 gap-y-2 gap-x-3;
}

.input {
  @apply col-span-2 md:col-span-1;
}

.rate {
  @apply col-span-2;
}

.button {
  @apply mt-auto md:mt-0;
}

.block {
  @apply flex flex-col gap-1;
}
</style>
