<script setup lang="ts">
import UiButton from "@/components/ui/Button/Button.vue";
import InputCurrency from "@/components/ui/Input/InputCurrency.vue";
import CryptoScreen from "@/components/Transaction/Latest/CryptoScreen.vue";
import Rate from "@/components/Transaction/Latest/Rate.vue";
import TransactionMessage from "@/components/Transaction/Latest/Message.vue";
import BigNumber from "bignumber.js";
import { onBeforeUnmount, ref, watch, watchEffect, computed } from "vue";
import { prepareAccountBalance } from "@/helpers";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";
import { DefaultExchangeFlow, EExchangeCurrency } from "@/helpers/exchange";

import type { TDepositByAnyCryptoState } from "@/components/Transaction/Latest/types";
import type { TExchangeResult } from "@/helpers/exchange/types";
import { useRoute, useRouter } from "vue-router";
import Calculation from "@/components/Calculation/Calculation.vue";
import CalculationValueRow from "@/components/CalculationValueRow/CalculationValueRow.vue";
import CalculationDivider from "@/components/CalculationDivider/CalculationDivider.vue";
import CalculationTotalRow from "@/components/CalculationTotalRow/CalculationTotalRow.vue";
import { useUserIsWarn } from "@/composable/useUserIsWarn";
import { useTracker, TrackerEvent } from "@/composable";

interface Props {
  gateway?: any;
  step: number;
  usdtAddress: string;
}

const props = defineProps<Props>();
const emit = defineEmits(["on-change-step", "on-close", "on-submit"]);
const { t } = useI18n();
const tracker = useTracker();
const exFlow = new DefaultExchangeFlow();
const defaultMinDepositSum = 51; // usd
const startDepositSum = 500;
const checkInterval = 10; // seconds

const defaultState: TDepositByAnyCryptoState = {
  fromValue: "0",
  toValue: "0",
  status: "new",
  network: "",
  address: "",
  addressHelper: null,
  pending: false,
  rate: 1,
  error: {},
};
const tState = ref<TDepositByAnyCryptoState>(
  JSON.parse(JSON.stringify(defaultState))
);
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const { userIsWarnEffect } = useUserIsWarn();
const inputState = ref<"from" | "to">("from");
const transactionCheckTimer = ref<ReturnType<typeof setInterval>>();
const timeLeftTimer = ref<number | undefined>(15); //minutes

const minDepositSum = computed<number>(() => {
  if (userIsWarnEffect.value) return 500;
  return defaultMinDepositSum;
});

const setRate = (fromAmount: number, toAmount: number) => {
  tState.value.rate = Number(
    prepareAccountBalance(String(toAmount / fromAmount), "USDT", true)
  );
};
const setFromValue = async (v: string) => {
  delete tState.value.error.inputTo;
  delete tState.value.error.inputFrom;
  tState.value.fromValue = new BigNumber(v);
  tState.value.pending = true;
  await exFlow
    .getExchangeRate(String(v), undefined)
    .then((result) => {
      if (result) {
        tState.value.toValue = new BigNumber(result.toAmount);

        setRate(Number(result.fromAmount), Number(result.toAmount));
        inputState.value = "from";
        validateTransaction();
      }
    })
    .catch((err) => {
      if (!err.message) {
        emit("on-submit", false, err.error);
        tState.value.error = err;
      } else {
        tState.value.error.inputFrom = t(
          `errors.exchange.${err.message.toLowerCase().replace(/ /g, "_")}`
        );
      }
    })
    .finally(() => {
      tState.value.pending = false;
      updateQuery();
    });
};
const setToValue = async (v: string) => {
  delete tState.value.error.inputTo;
  delete tState.value.error.inputFrom;

  tState.value.toValue = new BigNumber(v);
  tState.value.pending = true;
  await exFlow
    .getExchangeRate(undefined, String(Number(v)))
    .then((result) => {
      if (result) {
        tState.value.fromValue = new BigNumber(result.fromAmount);
        setRate(Number(result.fromAmount), Number(result.toAmount));
        inputState.value = "to";
        validateTransaction();
      }
    })
    .catch((err) => {
      if (!err.message) {
        emit("on-submit", false, err.error);
        tState.value.error = err;
      } else {
        tState.value.error.inputTo =
          Number(tState.value.toValue) >= minDepositSum.value
            ? t(
                `errors.exchange.${err.message
                  .toLowerCase()
                  .replace(/ /g, "_")}`
              )
            : t("errors.minimumAmount", {
                a: prepareAccountBalance(minDepositSum.value, "USDT"),
                c: " USDT",
              });
      }
    })
    .finally(() => {
      tState.value.pending = false;
      updateQuery();
    });
};

const setExchangeCurrencies = async () => {
  tState.value.pending = true;
  const fromCurrency = props.gateway.key.toUpperCase() as EExchangeCurrency;
  exFlow.setTo(EExchangeCurrency.USDTTRC20, props.usdtAddress);
  exFlow.setFrom(EExchangeCurrency[fromCurrency]);
  tState.value.pending = false;
};

const onStartDeposit = async () => {
  tState.value.pending = true;
  await exFlow
    .createExchange()
    .then((result: TExchangeResult) => {
      if (result.payinExtraIdName && result.payinExtraId)
        tState.value.addressHelper = {
          label: result.payinExtraIdName,
          value: result.payinExtraId,
        };
      else {
        tState.value.addressHelper = null;
      }
      if (result.payinAddress) {
        tState.value.address = result.payinAddress;
        tState.value.pending = false;
        emit("on-change-step", 2);
      }
      if (result) {
        tracker.logEvent(TrackerEvent.DEPOSIT_METHOD_CONTINUE, {
          crypto: result.fromNetwork,
        });
        // send notify
        exFlow.postNotify({
          email: String(userStore.user.email),
          sum: String(tState.value.toValue),
          from: props.gateway?.key,
          exchangeId: result.id,
        });
      }
    })
    .catch((reject) => {
      console.error("DepositByAnyCrypto->onStartDeposit: Error:" + reject);
    });
  onCheckStatusChange();
};

const onCheckStatusChange = () => {
  transactionCheckTimer.value = setInterval(async () => {
    const res = await exFlow.getStatus();
    if (tState.value.status !== res.status) {
      tState.value.status = res.status;
    }
  }, checkInterval * 1000);
};

const onTimeLeftFinish = () => {
  if (["new", "waiting"].includes(String(tState.value.status))) {
    clearInterval(transactionCheckTimer.value);
  }
};
const onTimeLeftReset = () => {
  clearInterval(transactionCheckTimer.value);
  emit("on-change-step", 1);
};

const validateTransaction = () => {
  const toValue = Number(tState.value.toValue);
  if (toValue < minDepositSum.value) {
    tState.value.error.inputTo = t("errors.minimumAmount", {
      a: prepareAccountBalance(minDepositSum.value, "USDT"),
      c: " USDT",
    });
  } else {
    delete tState.value.error.inputTo;
  }
  return;
};
const updateQuery = async () => {
  const query = Object.assign({}, route.query);
  query.to_amount = String(tState.value.toValue);
  await router.replace({ query });
};

const mark = computed(() => {
  return exFlow.getMark();
});

watchEffect(async () => {
  //   // clear check transaction status if gateway changed
  if (props.gateway.iso_code && props.step === 1) {
    await setExchangeCurrencies();
    if (
      Number(route.query.to_amount) <= startDepositSum ||
      !route.query.to_amount
    )
      await setToValue(String(startDepositSum));
  }
  if (props.step === 1) {
    tState.value.status = defaultState.status;
    clearInterval(transactionCheckTimer.value);
  }
  if (
    route.query.to_amount &&
    Number(route.query.to_amount) > startDepositSum &&
    props.gateway?.iso_code
  )
    await setToValue(String(route.query.to_amount));
});

watch(
  () => [tState.value.status],
  ([newStatus]) => {
    // remove timer if status !== ["new", "waiting"]
    if (!["new", "waiting"].includes(String(newStatus))) {
      timeLeftTimer.value = undefined;
    }
    // success screen
    if (newStatus === "finished") {
      emit("on-submit", true);
    }
  }
);

onBeforeUnmount(() => {
  clearInterval(transactionCheckTimer.value);
});
</script>

<template>
  <template v-if="props.step === 1">
    <div :class="depositAnyCrypto.block">
      <div :class="depositAnyCrypto.inputs">
        <InputCurrency
          :label="$t('Transfer amount')"
          :disabled="tState.pending"
          :value="tState.fromValue as number"
          :currency="String(props.gateway?.iso_code)"
          :class="depositAnyCrypto.input"
          :error="tState.error.inputFrom"
          @change="(v) => setFromValue(v)" />
        <InputCurrency
          :label="$t('label.amountEnrolled')"
          :disabled="tState.pending"
          :value="tState.toValue as number"
          :currency="'USDT'"
          :min="
            Number(tState.toValue) < 50
              ? Number(prepareAccountBalance(minDepositSum, 'USDT'))
              : undefined
          "
          :helper-text="
            Number(tState.toValue) === minDepositSum
              ? t('errors.minimumAmount', {
                  a: minDepositSum,
                  c: ' USDT',
                })
              : undefined
          "
          :error="tState.error.inputTo"
          :class="depositAnyCrypto.input"
          @change="(v) => setToValue(v)" />
        <Rate
          v-if="tState.rate"
          :rate="tState.rate"
          :from-currency="props.gateway?.iso_code"
          :to-currency="'USDT'"
          :class="depositAnyCrypto.rate" />
      </div>
    </div>
    <!-- Messages & Alerts -->
    <div :class="depositAnyCrypto.messages">
      <TransactionMessage
        :title="t('deposit-modal.important')"
        :class="depositAnyCrypto.message">
        <p>
          {{
            t("deposit.byAnyCryptoMessage", {
              f: props.gateway?.title,
              t: "USDT TRC20",
            })
          }}
        </p>
        <p class="mt-2">
          <i18n-t keypath="deposit-modal.exchange-service-message">
            <template #m>
              <a
                class="text-fg-blue underline"
                href="https://changenow.io/terms-of-use"
                target="_blank">
                {{ $t("deposit-modal.terms-of-use") }}
              </a>
            </template>
            <template #l>
              <a
                class="text-fg-blue underline"
                href="https://changenow.io/faq/kyc"
                target="_blank">
                {{ $t("deposit-modal.aml-policy") }}
              </a>
            </template>
          </i18n-t>
        </p>
      </TransactionMessage>
      <TransactionMessage
        v-if="userIsWarnEffect"
        type="alert"
        :title="t('transaction.depositWarning.attention')"
        :class="depositAnyCrypto.message">
        <p>
          {{ t("transaction.depositWarning.attention_text_warn") }}
        </p>
      </TransactionMessage>
    </div>
    <UiButton
      :loading="tState.pending"
      :disabled="
        Object.keys(tState.error).length !== 0 ||
        Number(tState.toValue) < minDepositSum
      "
      :title="$t('continue')"
      size="normal"
      :class="depositAnyCrypto.button"
      @click="onStartDeposit" />
  </template>
  <template v-if="props.step === 2">
    <div :class="depositAnyCrypto.block">
      <CryptoScreen
        :address="tState.address"
        :address-helper="tState.addressHelper"
        :network="props.gateway?.title"
        :timer="timeLeftTimer"
        :amount="Number(tState.fromValue)"
        :iso-code="props.gateway?.iso_code"
        :mark="mark"
        @on-timer-finish="onTimeLeftFinish"
        @on-timer-reset="onTimeLeftReset"
        @on-close="emit('on-close')" />
    </div>

    <Calculation>
      <CalculationValueRow :title="$t('transfer amount')">
        {{ tState.fromValue }} {{ props.gateway.iso_code }}
      </CalculationValueRow>
      <CalculationValueRow :title="$t('label.rate')">
        <span>1 {{ props.gateway?.iso_code }}</span>
        <span>&nbsp;=&nbsp;</span>
        <span>
          {{ prepareAccountBalance(parseFloat(`${tState.rate}`), "USDT") }}
          {{ "USDT" }}
        </span>
      </CalculationValueRow>
      <CalculationDivider />
      <CalculationTotalRow
        :title="$t('label.amountEnrolled')"
        :value="`${tState.toValue} USDT`" />
    </Calculation>

    <TransactionMessage
      :title="t('deposit-modal.important')"
      :class="depositAnyCrypto.message">
      <p>
        {{ $t("deposit-modal.networkMessage") }}
      </p>
      <p class="mt-2">
        {{
          $t("deposit-modal.amountMessage", {
            f: props.gateway?.title,
            t: "USDT TRC20",
          })
        }}
      </p>
    </TransactionMessage>
  </template>
</template>

<style lang="scss" module="depositAnyCrypto">
.messages {
  @apply flex flex-col gap-4;
}
.message {
  & ul,
  p {
    @apply text-4 leading-5;
  }

  & ul {
    @apply list-disc pl-4 flex flex-col gap-3;
  }

  a {
    text-underline-position: from-font;
    text-decoration-thickness: 1px;
  }
}

.inputs {
  @apply grid grid-cols-2 gap-y-2 gap-x-3;
}

.input {
  @apply col-span-2 md:col-span-1;
}

.rate {
  @apply col-span-2;
}

.button {
  @apply mt-auto md:mt-0;
}

.block {
  @apply flex flex-col gap-3;
}
</style>
