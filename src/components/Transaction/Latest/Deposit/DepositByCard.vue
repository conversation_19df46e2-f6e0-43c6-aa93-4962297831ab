<script setup lang="ts">
import { useAccountsLogic } from "@/composable/AccountsLogic/AccountsComposable";
import { computed, onMounted } from "vue";
import Env from "@/config/env";
import { SHA512, enc } from "crypto-js";
import { v4 as uuid } from "uuid";
import { useTracker, TrackerEvent } from "@/composable";
import { useI18n } from "vue-i18n";

const { locale } = useI18n();
const accountsLogic = useAccountsLogic();
const tracker = useTracker();
const widgetId = Env.mercuryoWidgetId;

const usdtAccount = computed(() => {
  return accountsLogic.list.value.find((a: any) => a.currency_id === 15);
});

const widgetLocale = computed(() => {
  const mercuryoLocales = [
    "de",
    "it",
    "en",
    "es",
    "nl",
    "fr",
    "ca",
    "he",
    "ja",
    "lv",
    "pt",
    "sr",
    "zh",
    "cs",
    "hu",
    "ka",
    "nb",
    "sk",
    "th",
    "fi",
    "is",
    "ko",
    "pl",
    "sl",
    "fa",
    "ru",
  ];
  return mercuryoLocales.includes(locale.value) ? locale.value : "en";
});

onMounted(() => {
  accountsLogic.updateAccounts().then(() => {
    let script = document.createElement("script");
    script.setAttribute("src", "https://widget.mercuryo.io/embed.2.0.js");

    script.onload = () => {
      if (!usdtAccount.value) {
        return;
      }

      const acc: any = usdtAccount.value;
      const address = (acc?.addresses[0]?.address || "").toString();
      const secret = SHA512(address + "xw12").toString(enc.Hex);
      const settings = {
        widgetId: widgetId,
        host: document.getElementById("mercuryo-widget"),
        type: "buy",
        currency: "USDT",
        network: "TRON",
        fixCurrency: true,
        ratesFeeOff: false,
        address: address,
        returnUrl: window.location.origin + "/app",
        merchantTransactionId: uuid().toString(),
        signature: secret,
        width: "100%",
        height: "100%",
        lang: widgetLocale.value,
        redirectUrl: window.location.origin + "/app",
        onStatusChange: function (data: any) {
          tracker.logEvent(TrackerEvent.CRYPTO_DEPOSIT_PROCESSING, data);
        },
      };

      // @ts-ignore
      mercuryoWidget.run(settings);
    };

    document.head.appendChild(script);
  });
});
</script>

<template>
  <div v-if="usdtAccount">
    <div id="mercuryo-widget" />
  </div>
</template>

<style>
#mercuryo-widget {
  height: 520px;
}
</style>
