<script lang="ts" setup>
/* eslint-disable max-len */
import UiButton from "@/components/ui/Button/Button.vue";
import SelectAccount from "@/components/ui/Select/SelectAccount.deprecated.vue";
import SelectCard from "@/components/ui/Select/SelectCard.vue";
import SelectCardOrAccount from "@/components/ui/Select/SelectCardOrAccount.vue";
import InputCurrency from "@/components/ui/Input/InputCurrency.vue";
import TransactionConfirm from "@/components/Transaction/Latest/Confirm.vue";
import TransactionMessage from "@/components/Transaction/Latest/Message.vue";
import { computed, onMounted, onUnmounted, ref, watch, watchEffect } from "vue";
import { capitalize } from "lodash";
import { useI18n } from "vue-i18n";
import { UserService } from "@modules/services/user";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { prepareAccountBalance } from "@/helpers/account";
import { useAxios } from "@/helpers/axios";
import { useUserStore } from "@/stores/user";
import { openPrivateLanding } from "@/helpers/events";
import { useTracker, TrackerEvent } from "@/composable";
import { secondsToDistance } from "@/helpers/time";
import type {
  TTFromDirection,
  TTransferProps,
  TTToDirection,
} from "@/components/Transaction/Latest/types";
import { useCardsStore } from "@/stores/cards";
import { useTransfer } from "@/composable/Transaction";
import { useRoute, useRouter } from "vue-router";
import AvoidCurrencyConversion from "@/components/Cards/StandardCreateCard/Stages/Issue/AvoidCurrencyConversion/Index.vue";
import TransactionDetails from "@/components/Transaction/Latest/TransactionDetails.vue";
import SaveMoneyPrivateTransfer from "@/components/Banners/SaveMoneyPrivateTransfer.vue";
import { PrivateTariffValues } from "@/constants/private_tariff_values";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useTariffGet } from "@/composable/API/useTariffGet";
import type { TTariffResource } from "@/types/api/TTariffResource";
import { calcAmountWithCommissionAndConversion } from "@/helpers/calcAmountWithCommissionAndConversion";
import { calcAmountWithReversedCommissionAndConversion } from "@/helpers/calcAmountWithReversedCommissionAndConversion";
import type { TAccount } from "@/types/TAccount";
import type { TCardPst } from "@/types/card";
import type { TCurrenciesCode } from "@/types/dictionary/currencies";

const { subscriptionsStatus } = useSubscriptionsInfo();
const userStore = useUserStore();
const tracker = useTracker();

// setup
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const props = defineProps<TTransferProps>();
const emit = defineEmits<{
  "on-change-step": [step: number];
  "on-submit": [success: boolean, error?: string | {}];
  "on-kyc-limit": [
    limit_type: "max-amount" | "min-status" | "other",
    message: string
  ];
  updateTransferTopUpFee: [
    amount: number,
    feeValue: number | string,
    feePercent: number,
    feeCurrency: TCurrenciesCode,
    feeCurrencySymbol: string
  ];
}>();

const MIN_TRANSFER_AMOUNT = 0.02;
const MIN_TRANSFER_TO_CARD_AMOUNT = 1.02;

const {
  state,
  rates,
  setToDirection,
  setFromDirection,
  setAccountForFromWithMaxUsd,
  setDefaultState,
  replaceQuery,
} = useTransfer();
const cardStore = useCardsStore("cards-transfer");
const tariff = ref<any>([]);
const tariffsDefault = ref<TTariffResource[]>([]);

const { accounts } = useUserAccounts();
const inputState = ref<"from" | "to">("from");

// setup

Promise.all([
  UserService.tariff(),
  UserService.exchangeRates(),
  useTariffGet(),
  UserService.info(),
  cardStore.getCards(),
  cardStore.getCards({ loadAllCards: true }),
]).then(([tariffRes, exchangeRes, tariffsDefaultRes]) => {
  const { data: allTariffsDefault } = tariffsDefaultRes;
  tariffsDefault.value = allTariffsDefault.value?.data || [];

  cardStore.state.loading = true;
  state.value.loading = true;
  if (!exchangeRes.status) {
    console.error(
      "Transfer->Promise.all error handled: ",
      exchangeRes.axiosResponse
    );
  }
  if (exchangeRes.data !== undefined) {
    rates.value = exchangeRes.data;
  }

  tariff.value = tariffRes.data;
  cardStore.state.loading = false;
  state.value.loading = false;
});
// computed
const isTransfer = props.type?.includes("transfer");
const isDeposit = props.type?.includes("deposit");

const tarrifsForExcludeDisplayPromoBanner = [
  "ultima-annually-3ds",
  "ultima-weekly-3ds",
  "ultima-3ds",
  "ultima-annually",
  "ultima-semiannually",
  "ultima-quarterly",
  "ultima-weekly",
  "ultima",
  "sigma_no_limits",
  "pst-black-prem",
  "exclusive",
  "pst-black",
  "3ds",
  "platinum-credit",
  "fb-prem",
  "adv",
  "pst-black-uniq-bin",
];

const canShowPrivateSaveMoneyBanner = computed(() => {
  return (
    transactionFeePercent.value > PrivateTariffValues.feeTopUpPercent &&
    !subscriptionsStatus.value &&
    !userStore.user?.is_old_user_for_subscription &&
    !userStore.isTeamMember &&
    !tarrifsForExcludeDisplayPromoBanner.includes(
      activeTo.value?._tariff_slug
    ) &&
    activeTo.value?.tariff_id > 10
  );
});

const privateSaveMoneyTopUpFee = computed(() => {
  return prepareAccountBalance(
    getFeeValue(Number(state.value.fromAmount)),
    String(activeFromAccount.value?._currency?.iso_code!),
    true
  );
});

const cards = computed<TCardPst[]>(() => {
  return cardStore.sortedCards;
});

const cardsAndAccountsForTo = computed<(TAccount | TCardPst)[]>(() => {
  let filteredAccounts: TAccount[] = [];
  let filteredCards: TCardPst[] = [];
  if (state.value.fromDirection === "account") {
    filteredAccounts = accounts.value.filter(
      (account: TAccount) => String(account.id) !== state.value.fromId
    );
  } else {
    filteredAccounts = accounts.value;
  }
  if (state.value.fromDirection === "card") {
    filteredCards = [...cards.value].filter(
      (card: TCardPst) =>
        String(card.id) !== state.value.fromId && card.status === 1
    );
  } else {
    filteredCards = [...cards.value].filter(
      (card: TCardPst) =>
        card.status === 1 || parseInt(card.account.balance) < 1
    );
  }
  return [...filteredAccounts, ...filteredCards];
});

const cardsAndAccountsForFrom = computed<any[]>(() => {
  const filteredAccounts =
    state.value.toDirection === "account"
      ? accounts.value.filter(
          (account: { id: number }) => String(account.id) !== state.value.toId
        )
      : accounts.value;
  const filteredCards =
    state.value.toDirection === "card"
      ? [...cardStore.sortedCards].filter(
          (card: TCardPst) => String(card.id) !== state.value.toId
        )
      : [...cardStore.sortedCards];
  return [...filteredAccounts, ...filteredCards];
});

const activeFrom = computed<any>(() => {
  if (state.value.fromDirection === "account") {
    return accounts.value.find(
      (account: { id: number }) => String(account.id) === state.value.fromId
    );
  } else if (state.value.fromDirection === "card") {
    return [...cardStore.sortedCards].find(
      (card: { id: number }) => String(card.id) === state.value.fromId
    );
  } else {
    return null;
  }
});
const activeTo = computed<any>(() => {
  if (state.value.toDirection === "account") {
    return accounts.value.find(
      (account: { id: number }) => String(account.id) === state.value.toId
    );
  } else if (state.value.toDirection === "card") {
    return [...cardStore.sortedCards].find(
      (card: { id: number }) => String(card.id) === state.value.toId
    );
  } else {
    return null;
  }
});
const activeFromAccount = computed<TAccount>(() => {
  return state.value.fromDirection === "account"
    ? activeFrom.value
    : activeFrom.value?.account;
});
const activeToAccount = computed<TAccount>(() => {
  return state.value.toDirection === "account"
    ? activeTo.value
    : activeTo.value?.account;
});

const currencyConversionRate = computed<number>(() => {
  if (!rates.value) return 1;
  // @ts-ignore
  const rate = rates.value[activeFromAccount.value?._currency?.iso_code];

  if (!rate) return 1;

  return Number(rate[activeToAccount.value?._currency?.iso_code] || 1);
});

const fromAmountMax = computed<number>(() => {
  if (state.value.fromDirection === "card") {
    return Number(activeFromAccount.value?.balance) - 1 > 0
      ? Number(activeFromAccount.value?.balance) - 1
      : 0;
  }
  return Number(activeFromAccount.value?.balance) || 0;
});

const transactionFeePercent = computed<number>(() => {
  const cardHaveSubscription: boolean = !!activeTo.value?.subscription_tariff;
  let tariffs: TTariffResource[];
  if (
    subscriptionsStatus.value &&
    !cardHaveSubscription &&
    !userStore.user?.is_old_user_for_subscription
  ) {
    tariffs = tariffsDefault.value;
  } else {
    tariffs = tariff.value;
  }
  if (!tariffs) return 0;
  const activeCardTariff: TTariffResource | undefined = tariffs.find(
    (v: TTariffResource) => v.id === activeTo.value.tariff_id
  );
  if (activeCardTariff === undefined) return 0;
  return Number(activeCardTariff.fee_topup) || 0;
});

// methods
const onInputSearchCardOrAccount = (v: string) => {
  cardStore.state.search = v;
  if (cardStore.state.search.length > 2 && cardStore.anotherCardsExist) {
    onSearchCardOrAccount();
  }
};

const onSearchCardOrAccount = async () => {
  cardStore.state.page = 1;
  await cardStore.getCards({
    params: {
      page: 1,
      search: cardStore.state.search,
      status: "",
    },
  });
};

const loadCards = async () => {
  try {
    if (
      cardStore.anotherCardsExist &&
      cardStore.state.page + 1 === cardStore.state.totalPages
    ) {
      cardStore.state.page = cardStore.state.page + 1;
      await cardStore.getCards({
        params: {
          search: cardStore.state.search,
          status: "",
        },
      });
    }
  } catch (ex) {
    console.warn(
      "WithdrawToCard->loadCards error handled: Can`t load cards: ",
      ex
    );
  }
};

const onSubmitTransaction = async () => {
  try {
    state.value.loading = true;
    state.value.isSubmit = true;
    state.value.isConfirm = false;

    const response = await useAxios().post("/transfer/transfer", {
      from_account_id: activeFromAccount.value.id,
      to_account_iban: activeToAccount.value.iban,
      to_user_email: undefined,
      amount: String(state.value.fromAmount),
      with_commission: "1",
    });
    if (response?.data?.success) {
      state.value.isConfirm = true;
    }
    emit(
      "updateTransferTopUpFee",
      Number(state.value.fromAmount),
      prepareAccountBalance(
        getFeeValue(Number(state.value.fromAmount)),
        String(activeFromAccount.value?._currency?.iso_code!),
        true
      ),
      transactionFeePercent.value,
      String(activeFromAccount.value?._currency?.iso_code),
      String(activeFromAccount.value?._currency?.symbol)
    );
    emit("on-submit", state.value.isConfirm);

    // TODO: add actual tracker
    tracker.logEvent(TrackerEvent.CARD_TOP_UP);
  } catch (ex: any) {
    const field = ex?.response?.data?.field;

    if (field) {
      if (field === "card_deposit_limit") {
        state.value.error.resultError = t("error.text.kyc_limit", {
          kyc_actual: capitalize(userStore.actual),
          kyc_deposit_limit:
            userStore?.userActualLimit?.userLimit.card_deposit_limit,
        });
        onKycLimit();
      } else {
        state.value.error[field] = field;
      }
    } else {
      state.value.error.resultError = ex?.response?.data?.message;
      emit("on-submit", !!state.value.isConfirm, state.value.error.resultError);
    }
  } finally {
    state.value.loading = false;
  }
};
const getFeeValue = (value: number) => {
  return value * (Number(transactionFeePercent.value) / 100);
};

const convertValue = (
  value: number,
  fromDir: TTFromDirection | string,
  toDir: TTToDirection | string,
  activeInput: "from" | "to"
) => {
  if (activeInput === "from") {
    if (fromDir === "account" && toDir === "account") {
      return value * currencyConversionRate.value;
    }
    if (fromDir === "account" && toDir === "card") {
      return calcAmountWithCommissionAndConversion(
        value,
        currencyConversionRate.value,
        transactionFeePercent.value,
        1
      );
    }
    if (fromDir === "card" && toDir === "account") {
      return value * currencyConversionRate.value;
    }
    if (fromDir === "card" && toDir === "card") {
      return calcAmountWithCommissionAndConversion(
        value,
        currencyConversionRate.value,
        transactionFeePercent.value,
        0
      );
    }
  } else if (activeInput === "to") {
    if (fromDir === "account" && toDir === "account") {
      return value / currencyConversionRate.value;
    }
    if (fromDir === "account" && toDir === "card") {
      return calcAmountWithReversedCommissionAndConversion(
        value,
        currencyConversionRate.value,
        transactionFeePercent.value,
        1
      );
    }
    if (fromDir === "card" && toDir === "account") {
      return value / currencyConversionRate.value;
    }
    if (fromDir === "card" && toDir === "card") {
      return calcAmountWithReversedCommissionAndConversion(
        value,
        currencyConversionRate.value,
        transactionFeePercent.value,
        0
      );
    }
  }

  return Number(value);
};

const changeFromValue = (newFromValue: number) => {
  if (
    !activeFromAccount.value?._currency?.iso_code ||
    !activeToAccount.value?._currency?.iso_code
  ) {
    return;
  }
  state.value.fromAmount = prepareAccountBalance(
    newFromValue,
    activeFromAccount.value._currency.iso_code
  );
  const toValue = convertValue(
    newFromValue,
    String(state.value.fromDirection),
    String(state.value.toDirection),
    "from"
  );
  state.value.toAmount = prepareAccountBalance(
    toValue > 0 ? toValue : 0,
    activeToAccount.value._currency.iso_code
  );
};

const changeToValue = (newToValue: number) => {
  if (
    !activeFromAccount.value?._currency?.iso_code ||
    !activeToAccount.value?._currency?.iso_code
  ) {
    return;
  }
  state.value.toAmount = prepareAccountBalance(
    newToValue,
    activeToAccount.value._currency.iso_code
  );
  const fromValue = convertValue(
    newToValue,
    String(state.value.fromDirection),
    String(state.value.toDirection),
    "to"
  );
  state.value.fromAmount = prepareAccountBalance(
    fromValue > 0 ? fromValue : 0,
    activeFromAccount.value._currency.iso_code
  );
};
const validateTransaction = () => {
  const fromValue = Number(state.value?.fromAmount ?? "0");
  const toValue = Number(state.value?.toAmount ?? "0");

  const activeFromAccountIsoCode = activeFromAccount.value?._currency?.iso_code;
  const activeToAccountIsoCode = activeToAccount.value?._currency?.iso_code;
  if (!activeFromAccountIsoCode || !activeToAccountIsoCode) return;

  const activeToAccountBalanceDefault = Number(
    activeToAccount.value?.balance_default
  );

  // Check max value for from direction
  if (fromValue > fromAmountMax.value) {
    state.value.error.selectFrom = t("errors.notEnoughFunds");
  } else {
    delete state.value.error.selectFrom;
  }

  const minTransferAmount =
    state.value.toDirection === "card"
      ? MIN_TRANSFER_TO_CARD_AMOUNT
      : MIN_TRANSFER_AMOUNT;

  if (activeFromAccountIsoCode !== "BTC") {
    // USD, EUR, USDT
    if (fromValue < minTransferAmount) {
      state.value.error.inputFrom = t("errors.minimumAmount", {
        a: `${minTransferAmount}`,
        c: activeFromAccount.value?._currency?.symbol,
      });
    } else {
      delete state.value.error.inputFrom;
    }
  } else {
    // BTC
    const conversionRate = rates.value
      ? Number(
          rates.value["BTC"][activeToAccountIsoCode as "USD" | "USDT" | "EUR"]
        ) ?? null
      : null;
    if (conversionRate) {
      const btcMinTransferAmount = minTransferAmount / conversionRate;
      if (fromValue > 0 && fromValue < btcMinTransferAmount) {
        state.value.error.inputFrom = t("errors.minimumAmount", {
          a: `${prepareAccountBalance(btcMinTransferAmount, "BTC")}`,
          c: activeFromAccount.value?._currency?.symbol,
        });
      } else {
        delete state.value.error.inputFrom;
      }
    }
  }

  // Check min value for to direction = card
  const balanceDifference = Math.abs(activeToAccountBalanceDefault - 1).toFixed(
    2
  );

  if (
    state.value.toDirection === "card" &&
    toValue > 0 &&
    activeToAccountBalanceDefault < 1 &&
    Number(balanceDifference) > toValue
  ) {
    state.value.error.inputTo = t("errors.minimumAmount", {
      a: balanceDifference,
      c: activeToAccount.value?._currency?.symbol,
    });
  } else {
    delete state.value.error.inputTo;
  }
};
watch(
  () => cardStore.state.search,
  (newSearch, oldSearch) => {
    if (oldSearch && !newSearch) {
      cardStore.getCards();
    }
  }
);
watch(
  () => [
    cardStore.state.loading,
    state.value.fromId,
    state.value.toId,
    state.value.fromAmount,
    state.value.toAmount,
  ],
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  ([_, newFromId, newToId]) => {
    //Check
    validateTransaction();

    /*
    if (newFromId || newToId) {
      validateTransaction();
    }
    */
    // watch from input
    if (
      newToId &&
      inputState.value === "from" &&
      Number(state.value.fromAmount) > 0
    ) {
      changeFromValue(Number(state.value.fromAmount));
    }
    // watch change direction
    if (
      (newFromId === state.value.toId &&
        state.value.toDirection === state.value.fromDirection) ||
      (newToId === state.value.fromId &&
        state.value.toDirection === state.value.fromDirection)
    ) {
      state.value.toId = "";
      state.value.fromAmount = "0";
      state.value.toAmount = "0";
    }
  }
);
const onKycLimit = () => {
  emit(
    "on-kyc-limit",
    "max-amount",
    t("error.text.kyc_limit", {
      kyc_actual: capitalize(userStore.actual),
      kyc_deposit_limit:
        userStore.userActualLimit?.userLimit.card_deposit_limit,
    })
  );
};
const cardDepositLimit = computed(() => {
  return Number(userStore.actualNum.deposit) > 0
    ? Number(userStore.actualNum.deposit)
    : 0;
});
onMounted(() => {
  state.value.type = props.type;
  // Setup from/to direction & set active card id for card store
  if (props.toId && props.toDirection === "card") {
    cardStore.state.activeCardId = props.toId;
  }
  if (props.fromId && props.fromDirection === "card") {
    cardStore.state.activeCardId = props.fromId;
  }
  if (props.toId && props.toDirection && state.value.toId === -1) {
    setToDirection(props.toId, props.toDirection);
  }
  if (isTransfer && props.fromId && props.fromDirection) {
    setFromDirection(String(props.fromId), props.fromDirection);
  }
});
watchEffect(async () => {
  if (state.value.toDirection === "card") {
    if (
      ["welcome", "scale"].includes(userStore.actual) &&
      parseFloat(Number(state.value?.toAmount).toFixed(2)) >
        cardDepositLimit.value
    ) {
      changeToValue(cardDepositLimit.value);
      onKycLimit();
    }
  }

  // set account with max usd for from direction in deposit
  if (isDeposit && state.value.fromId === -1 && rates.value) {
    state.value.loading = true;
    setAccountForFromWithMaxUsd(accounts.value);
    state.value.loading = false;
  }

  if (
    props.toAmount &&
    activeToAccount.value &&
    activeFromAccount.value &&
    state.value.toAmount === "0"
  ) {
    inputState.value = "to";
    changeToValue(Number(props.toAmount));
  }
  if (
    !props.toAmount &&
    props.fromAmount &&
    activeToAccount.value &&
    activeFromAccount.value &&
    state.value.fromAmount === "0"
  ) {
    inputState.value = "from";
    changeFromValue(Number(props.fromAmount));
  }

  if (state.value) {
    await replaceQuery(route, router);
  }
});
onUnmounted(() => {
  cardStore.setDefaultState();
  cardStore.$dispose();
  setDefaultState();
});
</script>
<template>
  <div :class="transfer.root">
    <template v-if="props.step === 1">
      <!--    From -->
      <!--      transfer-account-{card|account}-->
      <template v-if="state.fromDirection === 'account' && isTransfer">
        <SelectAccount
          v-if="state.fromId !== -1"
          :error="state.error.selectFrom"
          :label="$t('transactions-from')"
          :loading="state.loading || cardStore.state.loading"
          :options="accounts"
          :rates="rates"
          :value="state.fromId"
          backdrop
          searchable
          @change="(v) => setFromDirection(v, 'account')" />
      </template>
      <!--      transfer-card-{card|account}-->
      <template v-if="state.fromDirection === 'card' && isTransfer">
        <SelectCard
          v-if="state.fromId !== -1"
          :error="state.error.selectFrom"
          :label="$t('transactions-from')"
          :loading="state.loading || cardStore.state.loading"
          :options="cardStore.sortedCards"
          :value="state.fromId"
          backdrop
          searchable
          @change="(v) => setFromDirection(v, 'card')"
          @search="(v: string) => onInputSearchCardOrAccount(v)"
          @is-intersecting="loadCards" />
      </template>
      <!--      deposit-{card|account(usd/eur)}-->
      <template v-if="isDeposit">
        <SelectCardOrAccount
          v-if="cardsAndAccountsForFrom.length"
          :disabled="state.loading"
          :error="state.error.selectFrom"
          :group-active="state.fromDirection"
          :label="$t('transactions-from')"
          :loading="
            state.loading || cardStore.state.loading || state.fromId === -1
          "
          :options="cardsAndAccountsForFrom"
          :placeholder="$t('label.choose')"
          :rates="rates"
          :value="state.fromId"
          backdrop
          searchable
          @change="(id, direction) => setFromDirection(id, direction)"
          @search="(v) => onInputSearchCardOrAccount(v)"
          @is-intersecting="loadCards" />
      </template>
      <!--    To -->
      <SelectCardOrAccount
        v-if="cardsAndAccountsForTo.length"
        :disabled="state.loading"
        :group-active="state.toDirection"
        :label="$t('transactions-to')"
        :loading="
          state.loading || cardStore.state.loading || state.fromId === -1
        "
        :options="cardsAndAccountsForTo"
        :placeholder="$t('label.choose')"
        :rates="rates"
        :value="state.toId"
        backdrop
        searchable
        with-intersecting
        @change="(id, direction) => setToDirection(id, direction)"
        @search="(v) => onInputSearchCardOrAccount(v)"
        @is-intersecting="loadCards" />

      <!--      If User choose direction (To/From) - Set Values-->
      <template v-if="activeFrom && activeTo">
        <div :class="transfer.inputs">
          <InputCurrency
            :class="transfer.input"
            :currency="String(activeFromAccount._currency?.symbol!)"
            :error="state.error.inputFrom"
            :label="$t('Transfer amount')"
            :max="fromAmountMax"
            :value="state.fromAmount"
            data-cy="transfer-amount"
            @change="(v: any) => changeFromValue(v)"
            @focus="inputState = 'from'" />
          <InputCurrency
            :class="transfer.input"
            :currency="String(activeToAccount._currency?.symbol!)"
            :error="state.error.inputTo"
            :label="$t('label.amountEnrolled')"
            :min="
              state.error.inputTo
                ? Number(
                    Math.abs(
                      Number(activeToAccount?.balance_default) - 1
                    ).toFixed(2)
                  )
                : undefined
            "
            :value="state.toAmount"
            data-cy="receive-amount"
            @change="(v: any) => changeToValue(v)"
            @focus="inputState = 'to'" />

          <TransactionDetails
            :fee-currency="String(activeFromAccount._currency?.symbol ?? '')"
            :fee-percent="transactionFeePercent"
            :fee-value="
              prepareAccountBalance(
                getFeeValue(Number(state.fromAmount)),
                String(activeFromAccount._currency?.iso_code!)
              )
            "
            :from-currency="String(activeFromAccount._currency?.iso_code!)"
            :is-transfer-account-to-card="
              state.fromDirection === 'account' && state.toDirection === 'card'
            "
            :rate="
              prepareAccountBalance(
                Number(currencyConversionRate),
                String(activeToAccount._currency?.iso_code ?? '')
              )
            "
            :to-currency="String(activeToAccount._currency?.symbol!)" />
        </div>

        <div
          v-if="activeFromAccount._currency?.iso_code === 'USDT'"
          class="w-full">
          <AvoidCurrencyConversion />
        </div>

        <!--        Messages & Alerts -->
        <div :class="transfer.messages">
          <TransactionMessage
            v-if="state.fromDirection === 'card'"
            :class="transfer.message">
            <p>
              {{
                t("transfer.fromCardDurationMessage", {
                  t: secondsToDistance(
                    Number(userStore.user.from_card_delay) * 60
                  ),
                })
              }}
            </p>
            <UiButton
              v-if="!userStore.user.has_special"
              :class="[transfer.button, transfer['message-button']]"
              :title="t('btn.quickTransferWithPrivate')"
              size="normal"
              type="orange"
              @click="openPrivateLanding" />
          </TransactionMessage>
        </div>

        <UiButton
          :class="transfer.button"
          :disabled="
            Object.keys(state.error).length !== 0 || Number(state.toAmount) <= 0
          "
          :title="$t('continue')"
          size="normal"
          @click="emit('on-change-step', 2)" />
      </template>
    </template>
    <!--    Confirm Transaction-->
    <template v-if="props.step === 2">
      <TransactionConfirm
        :amount="Number(state.toAmount)"
        :amount-currency="activeToAccount?._currency?.symbol"
        :amount-iso-code="activeToAccount?._currency?.iso_code"
        :fee-currency="String(activeFromAccount._currency?.symbol ?? '')"
        :fee-percent="transactionFeePercent"
        :fee-value="
          prepareAccountBalance(
            getFeeValue(Number(state.fromAmount)),
            String(activeFromAccount._currency?.iso_code!)
          )
        "
        :from="activeFrom"
        :loading="state.loading"
        :to="activeTo"
        @on-submit="onSubmitTransaction" />

      <template v-if="canShowPrivateSaveMoneyBanner">
        <SaveMoneyPrivateTransfer
          :amount="Number(state.fromAmount)"
          :fee-currency="String(activeFromAccount?._currency?.iso_code)"
          :fee-currency-symbol="String(activeFromAccount?._currency?.symbol)"
          :top-up-fee="privateSaveMoneyTopUpFee"
          :top-up-fee-percent="transactionFeePercent" />
      </template>
    </template>
  </div>
</template>

<style lang="scss" module="transfer">
.root {
  @apply flex flex-col gap-8;
}

.inputs {
  @apply grid grid-cols-2 gap-x-3 gap-y-2;
}

.input {
  @apply col-span-2 md:col-span-1;
}

.fee,
.rate {
  @apply col-span-2;
}

.rate {
  @apply mt-3;
}

.button {
  @apply mt-auto md:mt-0;
}

.messages {
  @apply flex flex-col gap-3;
}

.message {
  &-button {
    @apply mt-4;
  }
}
</style>
