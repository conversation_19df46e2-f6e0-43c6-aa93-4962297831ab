<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UiSelect from "@/components/ui/Select/Select.vue";
import Warning from "@/components/ui/Warning/Warning.vue";
import CardInfo from "../../ui/CardInfo.vue";
import AccountInfo from "../../ui/AccountInfo.vue";
import TransactionConfirm from "../TransactionConfirm.vue";
import TransactionAlert from "../TransactionAlert.vue";

import { transfer as transferHelper } from "../../helpers/withdraw";
import TransferAmount from "../../ui/TransferAmount.vue";

import { prepareAccountBalance } from "@/helpers/account";
import { getPrettyRateSuffix } from "@/helpers/transactions";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { getCurrencyIcon, getAccountName } from "@/helpers";
import { useI18n } from "vue-i18n";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { useQueryClient } from "vue-query";
import { userAccountsKey } from "@/config/queryConfig";
import { useTracker, TrackerEvent } from "@/composable";
import { UserService } from "@modules/services/user";
import { useUserStore } from "@/stores/user";
import { useState } from "@/helpers/utilities";

// props
interface Props {
  activeAccount?: any;
  activeCard?: any;
}

const props = defineProps<Props>();
const queryClient = useQueryClient();
const { accounts } = useUserAccounts({ selectUsdAccount: true });
const tracker = useTracker();

// data
const { t } = useI18n();
const selectedAccountId = ref<string | number>("");
const [transferLoading, setTransferLoading] = useState<boolean>(false);
const depositForm = reactive<any>({
  fromAmount: 0,
  toAmount: 0,
  isConfirm: false,
  isSubmit: false,
  status: "success",
  error: "",
});
const rates = ref<any>({});
const tariff = ref<any>([]);
const activeFocus = ref<string>("");
const prettySuffix = ref<string | number>("");
const { userActualLimit } = useUserStore();

// setup
Promise.all([UserService.tariff(), UserService.exchangeRates()]).then(
  ([tariffResponse, exchange]) => {
    if (!exchange.status) {
      console.error(
        "DepositCardFromAccount->Promise.all_1 error handled: ",
        exchange.axiosResponse
      );
    }
    if (!tariffResponse.status) {
      console.error(
        "DepositCardFromAccount->Promise.all_2 error handled: ",
        tariffResponse.axiosResponse
      );
    }

    rates.value = exchange.data;
    tariff.value = tariffResponse.data;
  }
);

const transfer_fee = computed(() => {
  const activeCardTariff: any = tariff.value.find(
    (v: any) => v.id === props.activeCard.tariff_id
  );
  if (activeCardTariff === undefined) {
    return undefined;
  }
  return Number(activeCardTariff.fee_topup);
});

// computed
// TODO
const needCurrencyConversion = computed(() => {
  return (
    accountFromComputed.value?.account?._currency.id !==
    props.activeAccount.currency_id
  );
});
// TODO:
const accountFromComputed = computed<any>(() => {
  return accounts.value.find(
    (account: any) => account.id === selectedAccountId.value
  );
});
// TODO:
const currencyConversionRate = computed(() => {
  if (!rates.value) {
    return 1;
  }

  return Number(
    rates.value[accountFromComputed.value?._currency?.iso_code][
      props.activeAccount?._currency?.iso_code
    ] || 1
  );
});
const noActiveAccounts = computed(() =>
  accounts.value.filter((item) => item.id !== props.activeAccount.id)
);

// methods

// @ts-ignore
const setDepositForm = (v: any) => (depositForm[v.field] = v.payload);
const setActiveFocus = (value: string) => (activeFocus.value = value);

const transfer = async () => {
  setTransferLoading(true);

  await transferHelper(
    depositForm,
    accountFromComputed.value.id,
    props.activeAccount.iban,
    undefined,
    t
  );

  await queryClient.invalidateQueries([userAccountsKey]);
  tracker.logEvent(TrackerEvent.CARD_TOP_UP);

  setTransferLoading(false);
};

const getAccountUsdBalance = (account: any) => {
  return (
    parseFloat(account.balance) *
    (rates.value[account._currency.iso_code]?.USD || 1)
  ).toFixed(2);
};

watch(
  () => depositForm.fromAmount,
  (newValue) => {
    if (activeFocus.value === "from" && transfer_fee.value !== undefined) {
      depositForm.toAmount = prepareAccountBalance(
        Number(newValue) *
          currencyConversionRate.value *
          (1 - transfer_fee.value / 100),
        props.activeAccount._currency.iso_code
      );
    }
  }
);

watch(
  () => depositForm.toAmount,
  (newValue) => {
    if (activeFocus.value === "to" && transfer_fee.value !== undefined) {
      depositForm.fromAmount = prepareAccountBalance(
        Number(newValue) /
          currencyConversionRate.value /
          (1 - transfer_fee.value / 100),
        accountFromComputed.value._currency.iso_code
      );
    }
  }
);

const currentError = computed(() => {
  if (
    Number(depositForm.fromAmount) > Number(accountFromComputed.value.balance)
  ) {
    return (
      t("errors.amountExceedsBalance2") +
      ` ${accountFromComputed.value._currency.iso_code}`
    );
  }

  if (!userActualLimit?.canDepositForAmount(Number(depositForm.fromAmount))) {
    return "kyc_limit";
  }

  return null;
});

onMounted(() => {
  prettySuffix.value = getPrettyRateSuffix(0, 19);
});
</script>

<template>
  <div
    class="flex flex-col gap-5"
    data-cy="choose-account">
    <!--    account is not selected-->
    <template v-if="!selectedAccountId">
      <h4>{{ $t("Choose the account") }}</h4>
      <!--      account list-->
      <div class="py-5 grid grid-flow-row auto-rows-fr gap-3">
        <div
          v-for="(account, index) in noActiveAccounts"
          :key="index"
          :class="`
          cursor-pointer flex items-center gap-3 px-3 py-4 rounded-base border
           border-greyscale-200 hover:border-secondary-base transition-all`"
          @click="selectedAccountId = account.id">
          <DynamicIcon
            :name="`${getCurrencyIcon(account._currency!.iso_code, 'solid')}`"
            class="w-7 h-7" />

          <div class="text-base font-extrabold">
            {{ $t(getAccountName(account._currency!.iso_code)) }}
          </div>

          <div class="flex flex-col ml-auto font-medium text-right">
            <span class="text-base">
              {{
                account._currency?.iso_code === "BTC"
                  ? account.balance
                  : parseFloat(account.balance).toFixed(2)
              }}
              {{ account._currency?.iso_code }}
            </span>

            <span
              v-if="account._currency?.iso_code !== 'USD'"
              class="text-sm text-greyscale-600">
              {{ getAccountUsdBalance(account) }} USD
            </span>
          </div>
        </div>
      </div>
    </template>

    <!--    account selected -->
    <template v-if="selectedAccountId">
      <!--      account reselect-->
      <UiSelect
        :label="$t('Recharge account')"
        :value="selectedAccountId"
        :options="noActiveAccounts"
        height="73"
        backdrop
        @change="(v: any) => (selectedAccountId = v)">
        <template #withIcon="slotProps">
          <div class="cursor-pointer flex items-center gap-3 w-full">
            <DynamicIcon
              :name="`${getCurrencyIcon(
                slotProps.option?._currency?.iso_code,
                'solid'
              )}`"
              class="w-7 h-7" />
            <div class="text-base font-extrabold">
              {{ $t(getAccountName(slotProps.option?._currency?.iso_code)) }}
            </div>
            <div class="flex flex-col ml-auto font-medium text-right">
              <span class="text-base"
                >{{ slotProps.option?.balance }}
                {{ slotProps.option?._currency?.iso_code }}</span
              >
              <span
                v-if="slotProps.option?._currency?.iso_code !== 'USD'"
                class="text-sm text-greyscale-600">
                {{ getAccountUsdBalance(slotProps.option) }} USD
              </span>
            </div>
          </div>
        </template>
      </UiSelect>

      <!--      account reselect-->
      <!--      <SelectAccount-->
      <!--        :label="$t('Recharge account')"-->
      <!--        :value="selectedAccountId"-->
      <!--        :options="accounts"-->
      <!--        :rates="rates"-->
      <!--        backdrop-->
      <!--        searchable-->
      <!--        @change="(v) => (selectedAccountId = v)"-->
      <!--      />-->

      <!--      inputs with focus magic -->
      <TransferAmount
        v-if="userActualLimit"
        :withdraw-form="depositForm"
        :currency-iso-code="{
          from: accountFromComputed?._currency.iso_code,
          to: props.activeCard.account._currency.iso_code,
        }"
        :max="
          userActualLimit.getMinAvailableDepositAmount(
            Number(accountFromComputed.balance),
            Number(activeAccount.balance)
          )
        "
        :errors="currentError"
        :limits="{
          kyc_actual: userActualLimit?.userLimit?.slug,
          kyc_deposit_limit: userActualLimit?.userLimit?.card_deposit_limit,
        }"
        @set-withdraw-form="setDepositForm"
        @set-max="
          depositForm.fromAmount = prepareAccountBalance(
            userActualLimit?.getMinAvailableDepositAmount(
              Number(accountFromComputed.balance),
              Number(activeAccount.balance)
            ) || 0,
            accountFromComputed?._currency?.iso_code || 'USD'
          )
        "
        @set-active-focus="setActiveFocus">
        <div class="flex items-center flex-col justify-between gap-2 w-full">
          <div class="flex items-start justify-between w-full">
            <span class="font-semibold text-greyscale-600">
              {{ $t("Transfer fee") }}
            </span>

            <span class="font-medium">{{ transfer_fee?.toFixed(1) }}%</span>
          </div>

          <div
            v-if="needCurrencyConversion"
            class="flex items-start justify-between w-full">
            <span class="font-semibold text-greyscale-600">
              {{ $t("Rate") }}
            </span>

            <span class="font-medium">
              1 {{ activeAccount._currency.iso_code }} =
              {{
                currencyConversionRate.toFixed(
                  accountFromComputed._currency.iso_code === "BTC" ? 6 : 2
                )
              }}
              <!--Pretty rate for USDT -> USD -> USDT-->
              {{
                accountFromComputed._currency.iso_code === "USDT"
                  ? prettySuffix
                  : ""
              }}

              {{ accountFromComputed._currency.iso_code }}
            </span>
          </div>
        </div>
      </TransferAmount>
    </template>

    <!--    trans confirm-->
    <TransactionConfirm
      v-if="depositForm.isConfirm"
      type="deposit"
      :from-amount="depositForm.fromAmount"
      :to-amount="depositForm.toAmount"
      :from-currency="accountFromComputed?._currency.iso_code"
      :to-currency="props.activeCard.account._currency.iso_code"
      :from-account-i-d="accountFromComputed.id"
      :to-account-i-b-a-n="props.activeAccount.iban"
      :rate="currencyConversionRate"
      @confirm="transfer"
      @back="depositForm.isConfirm = false">
      <template #from>
        <AccountInfo
          :account="accountFromComputed"
          :usd-balance="getAccountUsdBalance(accountFromComputed)" />
      </template>
      <template #to>
        <CardInfo :card="props.activeCard" />
      </template>
      <template #toAlert>
        <!--  message for blocked cards-->
        <Warning
          v-if="props.activeCard?.status === 10"
          error="deposit_to_closed_card"
          template="wrap"
          background="yellow" />
      </template>
    </TransactionConfirm>

    <!--    trans alert-->
    <TransactionAlert
      v-if="depositForm.isSubmit"
      :loading="transferLoading"
      :type="depositForm.status"
      :error="depositForm.error"
      @close="$emit('close')" />
  </div>
</template>
