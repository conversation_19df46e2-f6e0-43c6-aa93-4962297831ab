<script setup lang="ts">
import UiButton from "@/components/ui/Button/Button.vue";
import InputCurrency from "@/components/ui/InputCurrency/InputCurrency.vue";
import GatewayToogle from "./GatewayToogle.vue";
import DepositWarning from "@/components/ui/Warning/DepositWarning.vue";

import { allGatewayTypes } from "@/config/accounts";
import { onMounted, reactive, watch, ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useTracker, TrackerEvent } from "@/composable";
import { UserService } from "@modules/services/user";
import { useUserStore } from "@/stores/user";
import { attentions } from "@/config/attentions";
interface Props {
  activeAccount?: any;
}

const props = withDefaults(defineProps<Props>(), {
  activeAccount: undefined,
});

const emit = defineEmits(["confirm"]);

const { t } = useI18n();

const userStore = useUserStore();
const tracker = useTracker();
const userIsWarn = computed<boolean>(() => !!userStore?.user?.show_warn);

const depositForm = reactive<any>({
  step: 1,
  error: "",
  amount: 0,
  activeGateway: "",
  gateway: {
    key: "",
    title: "",
    country: null,
    bankAccountType: null,
    cryptoRequest: "",
    systemRequest: "",
  },
  isConfirm: false,
});
const rates = ref<any>({});

const validateAmount = () => {
  if (Number(depositForm.amount) === 0) {
    depositForm.error = t("deposit.amount.error");
  }
};
const getAllGatewaysTypes = async () => {
  validateAmount();

  if (!depositForm.error) {
    depositForm.step++;
  }
};

const getGatewaysByType = (gatewayType: string) => {
  return allGatewayTypes.find((type: any) => type.key === gatewayType);
};

const setGateway = (gateway: any) => {
  depositForm.activeGateway = gateway.key;
  depositForm.gateway.key = gateway.key;
  depositForm.gateway.title = gateway.title;
};

const setExtra = (country: any, type: any, crypto: any, system: any) => {
  depositForm.gateway.country = country;
  depositForm.gateway.bankAccountType = type;
  depositForm.gateway.cryptoRequest = crypto;
  depositForm.gateway.systemRequest = system;
  depositForm.isConfirm = true;
};

const onConfirm = async () => {
  emit("confirm", depositForm.gateway.key, depositForm.amount);

  const methodType = getGatewaysByType(depositForm.activeGateway);

  await tracker.logEvent(TrackerEvent.PAYMENT_METHOD_SELECTED, {
    amount: depositForm.amount,
    method: depositForm.gateway.key,
    methodType: methodType?.key,
    bankCountry: depositForm.gateway.country.title || null,
    bankAccountType: depositForm.gateway.bankAccountType?.title || null,
    cryptoRequest: depositForm.gateway.cryptoRequest?.text || null,
    systemRequest: depositForm.gateway.systemRequest?.text || null,
  });
};

watch(
  () => depositForm.amount,
  (newAmount) => {
    try {
      if (Number(newAmount) > 0) {
        depositForm.error = "";
      }
    } catch (ex) {
      console.warn("DepositToFiat->watch error handled: ", ex);
    }
  }
);

// get user exchange rates
UserService.exchangeRates().then((res) => {
  if (res.status) {
    rates.value = res.data;
  }
});

onMounted(() => {
  window.addEventListener("keydown", (e) => {
    if (e.key === "Enter") {
      if (depositForm.step === 1) {
        getAllGatewaysTypes();
      }
    }
  });
});

const activeCrypto = computed(() => {
  return props.activeAccount?._currency?.iso_code;
});

const showDepositWarnForCrypto = computed(() => {
  if (activeCrypto.value === "BTC") {
    return attentions.btc;
  }
  return attentions.usdt_trc20;
});

defineExpose({ depositForm });
</script>

<template>
  <div class="flex flex-col gap-10">
    <div class="flex flex-col">
      <!--      Deposit amount-->
      <template v-if="!depositForm.isConfirm">
        <h6>{{ $t("Amount") }}</h6>

        <InputCurrency
          :value="depositForm.amount"
          :currency="
            props.activeAccount._currency.symbol ||
            props.activeAccount._currency.iso_code
          "
          :error="depositForm.error"
          align="left"
          should-auto-focus
          class="mb-6"
          data-cy="deposit-amount"
          @blur="validateAmount"
          @change="(v) => (depositForm.amount = v)" />
      </template>
      <!--      Deposit amount-->

      <template v-if="userIsWarn">
        <!-- If the user is in the waiting area, then by the number of views. -->
        <DepositWarning
          class="mb-5"
          :text="
            $t('transaction.depositWarning.attention_text_warn', {
              f: '500',
              s: '100%',
              t: '$1000',
              fo: '0%',
            })
          " />
        <DepositWarning
          v-if="activeCrypto === 'BTC' && showDepositWarnForCrypto"
          :text="
            $t('transaction.depositWarning.attention_text_btc', {
              f: '$1000',
              s: '$10',
              t: '$1000',
              fo: '0%',
            })
          " />
      </template>
      <template v-else>
        <DepositWarning
          v-if="showDepositWarnForCrypto"
          :text="
            activeCrypto === 'BTC'
              ? $t('transaction.depositWarning.attention_text_btc', {
                  f: '$1000',
                  s: '$10',
                  t: '$1000',
                  fo: '0%',
                })
              : undefined
          " />
      </template>

      <!-- Step 1: Set deposit amount -->
      <template v-if="depositForm.step === 1">
        <UiButton
          :title="$t('deposit.select_method')"
          data-cy="deposit-select-method-btn"
          class="mt-6"
          @click="getAllGatewaysTypes" />
      </template>
      <!-- Step 1: Set deposit amount -->

      <!--      Step 2: Set deposit gateway-->
      <template v-if="depositForm.step === 2">
        <div class="flex flex-col gap-10 mb-10">
          <h4>{{ $t("deposit.methods") }}:</h4>
          <GatewayToogle
            v-for="(type, index) in allGatewayTypes"
            :key="index"
            :gateway-type="getGatewaysByType(type.key)"
            :active-gateway="depositForm.activeGateway"
            @set-gateway="setGateway"
            @set-extra="setExtra"
            @confirm="onConfirm" />
        </div>
      </template>
      <!--      Step 2: Set deposit gateway-->
    </div>
  </div>
</template>
