<script lang="ts" setup>
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";
import { Tooltip } from "floating-vue";
import { RouteName } from "@/constants/route_name";
import { isMobile } from "@/helpers/breakpoints";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useSubscriptionsList } from "@/composable/useSubscriptionsList";
import { useUserTariff } from "@/composable/useUserTariff";
import { useBinsFromConfig } from "@/composable/useBinsFromConfig";
import { useCardBinsBlacklistGet } from "@/composable/API/useCardBinsBlacklistGet";
import {
  useCountrySets,
  useSubscriptionPlusCardAutobuy1Experiment,
  useVerificationActualGet,
  useTracker,
  TrackerEvent,
} from "@/composable";
import { useKycLimitDialog } from "@/composable/useKycLimitDialog";
import type { TBinInfo, TSelectedBin } from "@/components/CreateCardV2/types";
import type { TCardTariffSlug } from "@/composable/Tariff/types";
import type { TTariffResource } from "@/types/api/TTariffResource";
import type { TUI2TableColumn } from "@/components/ui2/UI2Table/types";
import type { TUI2DialogBtnConfig } from "@/components/ui2/UI2Dialog/types";
import Loader from "@/components/ui/Loader/Loader.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import PaymentSystemBadge from "@/components/PaymentSystemBadge/PaymentSystemBadge.vue";
import UI2Title from "@/components/ui2/UI2Title/UI2Title.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import UI2Table from "@/components/ui2/UI2Table/UI2Table.vue";
import UI2MessageBlock from "@/components/ui2/UI2MessageBlock/UI2MessageBlock.vue";
import UI2Badge from "@/components/ui2/UI2Badge/UI2Badge.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import UI2Dialog from "@/components/ui2/UI2Dialog/UI2Dialog.vue";

const props = defineProps<{
  slug: TCardTariffSlug;
  isAdvWithSubActive: boolean;
  isAutobuy: boolean;
}>();

const emit = defineEmits<{
  selectBin: [v: TSelectedBin];
}>();

const { t } = useI18n();
const router = useRouter();
const tracker = useTracker();
const { isTeamMember } = storeToRefs(useUserStore());
const { userTariffData } = useUserTariff();
const { data: verificationData } = useVerificationActualGet();
const { getValue, userAdvCardsCount } =
  useSubscriptionPlusCardAutobuy1Experiment();
const { isActive: isSubscriptionPlusCardAutobuy1ExperimentActive } = getValue();
const { subscriptionsStatus, isFetching: isSubscriptionsFetching } =
  useSubscriptionsInfo();
const { currentMainTariff, isFetching: isCurrentMainTariffFetching } =
  useSubscriptionsList();
const { bins: binsFromConfig, isFetching: isBinsFetching } = useBinsFromConfig(
  props.slug === "ultima-3ds" ? "ultima" : props.slug
);
const { data: binsBlacklist, isFetching: isBinsBlacklistFetching } =
  useCardBinsBlacklistGet();

const selectedBin = ref<TSelectedBin>();

const selectedTariff = computed<TTariffResource | undefined>(() => {
  const tariff = userTariffData.data?.value?.data.find(
    (tariff) => tariff.slug === props.slug
  );
  if (props.isAdvWithSubActive && props.isAutobuy) {
    tariff!.fee_topup = "6";
    tariff!.fee_transaction_amount = "0";
  }

  return tariff;
});

const isExperimentalAutobuy = computed(() => {
  return (
    (props.isAutobuy || userAdvCardsCount.value <= 0) &&
    Boolean(isSubscriptionPlusCardAutobuy1ExperimentActive.value)
  );
});

// ============== Table
const bins = computed<TBinInfo[]>(() => {
  return binsFromConfig.value
    .map((binItem) => {
      const blackListItem = binsBlacklist.value?.data?.find((blItem) => {
        return binItem.bin.startsWith(blItem.bin);
      });
      if (
        blackListItem &&
        (blackListItem.disabled_for.includes(
          currentMainTariff.value?.subscription_tariff.name || ""
        ) ||
          !currentMainTariff.value)
      ) {
        return {
          ...binItem,
          blackListed: true,
          blackListedLabel: blackListItem.label,
          disabled_for: blackListItem.disabled_for,
          default: {
            depositFee: selectedTariff.value?.fee_topup,
            monthlyPayment: selectedTariff.value?.card_price,
          },
        };
      }

      return {
        ...binItem,
        blackListed: false,
      };
    })
    .map((item) => ({
      ...item,
      ...(subscriptionsStatus.value ? { private: false } : {}),
    }))
    .sort((a) => (a.blackListed ? 1 : -1));
});

const syntheticPrivateBins = computed<TBinInfo[]>(() => {
  if (!selectedTariff.value || isExperimentalAutobuy.value) {
    return [];
  }

  return bins.value
    .filter((bin) => !bin.blackListed)
    .map((bin) => ({
      ...bin,
      blackListed: true,
      default: {
        depositFee: selectedTariff.value?.fee_topup,
        monthlyPayment: selectedTariff.value?.card_price,
      },
    }));
});

const totalBinsList = computed<TBinInfo[]>(() => {
  return subscriptionsStatus
    ? bins.value
    : [...bins.value, ...syntheticPrivateBins.value];
});

const columns = computed<TUI2TableColumn<TBinInfo>[]>(() => {
  return [
    {
      label: t("label.bin"),
      key: "bin",
    },
    {
      label: t("label.paymentSystem"),
      key: "tariffs",
    },
    {
      label: "",
      key: "default",
      align: "right",
    },
  ];
});

const isTableLoading = computed<boolean>(() => {
  return (
    isSubscriptionsFetching.value ||
    isCurrentMainTariffFetching.value ||
    isBinsBlacklistFetching.value
  );
});

const isNewBin = (item: TBinInfo) => {
  return item.featured && item.bin === "542723";
};

const isAuto3ds = (bin: string): boolean => {
  return ["542723", "517746"].includes(bin);
};
// ---- end Table

const isUniversalAdvTariff = computed<boolean>(() => {
  return props.slug === "adv" || props.slug === "advertisement-cards";
});

// ============== Under Maintenance Dialog
const isOpenSelectBinUnderMaintenanceModal = ref<boolean>(false);

const confirmSelectBinUnderMaintenance = () => {
  if (!selectedBin.value) return;
  emit("selectBin", selectedBin.value);
};

const cancelSelectBinUnderMaintenance = () => {
  isOpenSelectBinUnderMaintenanceModal.value = false;
};

const underMaintenanceConfirmBtnConfig: TUI2DialogBtnConfig = {
  label: t("cards.release-it-anyway"),
  callback: confirmSelectBinUnderMaintenance,
};

const underMaintenanceCancelBtnConfig: TUI2DialogBtnConfig = {
  label: t("cards.select-another-card"),
  callback: cancelSelectBinUnderMaintenance,
};
// ---- end Under Maintenance Dialog

// ============== Member Blacklist info Dialog
const isOpenMemberDialog = ref<boolean>(false);

const onCloseMemberDialog = () => {
  isOpenMemberDialog.value = false;
};

const memberDialogBtnConfig: TUI2DialogBtnConfig = {
  label: t("OK"),
  callback: onCloseMemberDialog,
};
// ---- end Member Blacklist info Dialog

// ============== Select Bin Handle
const onSelectBin = async (item: TBinInfo) => {
  // Everything is OK with BIN
  if (!item.blackListed && !item?.under_maintenance) {
    selectedBin.value = {
      bin: item.bin,
      slug: item.slug as TCardTariffSlug,
    };
    emit("selectBin", selectedBin.value);
    return;
  }

  // BIN is under maintenance
  if (item?.under_maintenance) {
    selectedBin.value = {
      bin: item.bin,
      slug: item.slug as TCardTariffSlug,
    };
    isOpenSelectBinUnderMaintenanceModal.value = true;
    return;
  }

  // BIN in blacklist
  if (item.blackListed) {
    if (isTeamMember.value) {
      isOpenMemberDialog.value = true;
    } else {
      await router.push({ name: RouteName.SUBSCRIPTION_TARIFF });
      await tracker.logEvent(TrackerEvent.BUTTON, {
        page: "/app/create",
        page_version: 0,
        name: "Private",
        version: 0,
        location: "table",
      });
    }
  }
};

const cardsByVerificationCount = computed<number | null>(() => {
  return verificationData.value?.data?.remaining_cards ?? null;
});
const { openKycLimitDialog } = useKycLimitDialog();
const { countrySetGuard, isActive: needVerificationAction } = useCountrySets();

/** Wrapper over `onSelectBin` with verification limit check and countrySetGuard */
const onSelectBinHandle = async (item: TBinInfo) => {
  if (cardsByVerificationCount.value === 0) {
    openKycLimitDialog("cards");
    return;
  }

  if (needVerificationAction.value) {
    await onSelectBin(item);
    return;
  }
  countrySetGuard(
    () => {
      onSelectBin(item);
    },
    async () => {
      await router.push({ name: RouteName.DASHBOARD });
    }
  );
};
// ---- end Select Bin Handle
</script>

<template>
  <div class="select-bin-step">
    <Loader v-if="isBinsFetching" />
    <template v-else-if="bins.length">
      <UI2Title
        :title="$t('create-card.bin-selection-title')"
        :subtitle="$t('create-card.bin-selection-subtitle')"
        :size="isMobile ? 's' : 'l'" />

      <UI2Table
        :items="totalBinsList"
        :columns="columns"
        grid-class="grid-cols-[1fr_1fr_108px] md:grid-cols-[140px_1fr_108px]"
        :get-key="(item) => item.bin"
        :is-loading="isTableLoading">
        <!-- BIN -->
        <template #bin="{ cell }">
          <UI2Text
            class="px-1"
            :class="
              isNewBin(cell) ? 'text-fg-accent-orange' : 'text-fg-base-primary'
            ">
            {{ cell.bin }}
          </UI2Text>
          <Tooltip
            v-if="isNewBin(cell)"
            class="ml-1"
            placement="bottom"
            :triggers="['hover']">
            <span class="cursor-pointer">
              <DynamicIcon
                name="star-filled"
                class="size-4 text-fg-accent-orange-primary" />
            </span>
            <template #popper>
              <div class="text-white text-3 leading-4 max-w-[13rem] p-1">
                {{ t("cards.exclusive-bin") }}
              </div>
            </template>
          </Tooltip>
        </template>

        <!-- Payment system-->
        <template #tariffs="{ cell }">
          <PaymentSystemBadge :mask="cell.bin" />

          <UI2Badge
            v-if="cell.autoSecure || isAuto3ds(cell.bin)"
            class="ml-1 whitespace-nowrap"
            color="white"
            has-border>
            3–DS
          </UI2Badge>
        </template>

        <!-- Choose BIN button -->
        <template #default="{ cell }">
          <UI2Button
            v-if="cell.blackListed"
            class="w-full"
            variant="primary"
            size="xs"
            @click="onSelectBinHandle(cell)">
            {{ cell.blackListedLabel || "Private" }}
          </UI2Button>

          <UI2Button
            v-else
            class="w-full"
            variant="secondary"
            size="xs"
            @click="onSelectBinHandle(cell)">
            {{ $t("label.choose") }}
          </UI2Button>
        </template>
      </UI2Table>

      <div class="messages">
        <UI2MessageBlock
          type="warning"
          :title="$t('create-card.select-bin.warning-msg-title')"
          :subtitle="$t('create-card.select-bin.warning-msg-text')" />

        <UI2MessageBlock
          v-if="selectedTariff"
          :title="$t('label.information')">
          <template #subtitle>
            <div>
              {{
                $t("createCard.warnMessage.onlyAdv", {
                  a: selectedTariff.name,
                  b: "30%",
                })
              }}
            </div>

            <div
              v-if="!isUniversalAdvTariff"
              class="mt-4.5">
              {{
                t("createCard.warnMessage.onlyAdvBySlug", {
                  a: selectedTariff.name,
                  b: "5%",
                })
              }}
            </div>
          </template>
        </UI2MessageBlock>
      </div>
    </template>

    <UI2Dialog
      :is-open="isOpenSelectBinUnderMaintenanceModal"
      :title="$t('cards.technical-work-is-underway')"
      :text="$t('cards.payment-from-card-not-possible-during-technical-works')"
      :confirm-btn-config="underMaintenanceConfirmBtnConfig"
      :cancel-btn-config="underMaintenanceCancelBtnConfig"
      @close="cancelSelectBinUnderMaintenance" />

    <UI2Dialog
      :is-open="isOpenMemberDialog"
      :title="$t('cards.member-modal.title')"
      :text="$t('cards.member-modal.text')"
      :confirm-btn-config="memberDialogBtnConfig"
      @close="onCloseMemberDialog" />
  </div>
</template>

<style lang="scss" scoped>
.select-bin-step {
  @apply flex flex-col gap-10 max-w-[31.25rem] mx-auto;
}

.messages {
  @apply flex flex-col gap-2;
}
</style>
