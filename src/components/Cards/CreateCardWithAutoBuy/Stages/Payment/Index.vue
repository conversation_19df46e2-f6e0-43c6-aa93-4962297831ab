<script setup lang="ts">
import {
  conversionCurrency,
  type TIsoCode,
  type TypeExchangeRates,
  useAccount,
} from "@/composable/useAccounts";
import QrCode from "qrcode";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { computed, onUnmounted, reactive, ref, watch } from "vue";
import type { TCardTariffSlug } from "@/composable/Tariff";
import {
  checkAndCreateAutoBuy,
  checkAndDeleteAutoBay,
  checkAutoBuyTimer,
  checkAutoBuyTransaction,
  type IAutoBuyStatus,
} from "@/composable/CardAutoBuy/";
import type {
  IAutoBayStoreDto,
  TAutoBuyStatusCode,
} from "@modules/services/autoBuy";
import ExperimentsService from "@/services/ExperimentsService";
import { copyToClipBoard } from "@/helpers/copyToClipboard";
import { UserService } from "@modules/services/user";
import { TrackerEvent, useTracker } from "@/composable";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { isNil } from "lodash";

const router = useRouter();
const { userFees, getUserFees } = useUserStore();
const tracker = useTracker();

interface Props {
  deposite: string;
  promoCode?: string;
  slug: TCardTariffSlug;
  selectedBin: string;
}

const props = defineProps<Props>();

interface IPaymentOption {
  label: string;
  icon: string;
  isoCode: TIsoCode;
}

//select
const accountOptionValue = ref<IPaymentOption>({
  label: "Tether (TRC20)",
  icon: "usdt-trc20",
  isoCode: "USDT",
});
const accountSelectOptions = ref<IPaymentOption[]>([
  {
    label: "Tether (TRC20)",
    icon: "usdt-trc20",
    isoCode: "USDT",
  },
  {
    label: "BTC",
    icon: "btc",
    isoCode: "BTC",
  },
]);

//rates
const rates = await UserService.exchangeRates();

const comissionUsdDepositingBtc = 10;

const totalPaymentUsd = computed(() => {
  let value = Number(props.deposite) || 0;

  if (accountOptionValue.value.isoCode === "BTC") {
    value *= 1.03; //To correct BTC volatility
    if (value < 1000) {
      value += comissionUsdDepositingBtc;
    }
  }

  return value ? value : 0;
});

const exchangeAmount = computed(() => {
  const value = conversionCurrency(
    totalPaymentUsd.value,
    accountOptionValue.value.isoCode,
    rates.data as TypeExchangeRates
  );
  if (accountOptionValue.value.isoCode === "BTC") {
    return Number(value).toFixed(8);
  } else {
    return Number(value).toFixed(2);
  }
});

//auto buy status
const currentAutoBuyStatus = reactive<IAutoBuyStatus>({
  status: 10,
  title: "Pending",
});
//auto buy payload config
const autoBuyStoreConfig = reactive<IAutoBayStoreDto>({
  type: props.slug,
  start_balance: String(props.deposite),
  code: props.promoCode ? props.promoCode : "",
  bin: props.selectedBin !== "" ? Number(props.selectedBin) : undefined,
});

//timers
const autoBuyCheckTimer = ref<any>(null);
const transactionCheckTimer = ref<any>(null);

//account data
const accounts = useAccount();

const accountsWithAddress = await accounts.getAllWithAddress();

const currentAccount = ref(
  accounts.findAccountByISO("USDT", accountsWithAddress!)
);
//current account address
const currentAccountAddress = computed(
  () => currentAccount.value?.addresses[0]["address"]!
);
autoBuyStoreConfig.account_id = currentAccount.value?.id;

//qr code generate
const qrCodeURL = ref("");
const generateQrCodeURL = (address: string) => {
  QrCode.toDataURL(address).then((data) => (qrCodeURL.value = data));
};
generateQrCodeURL(currentAccountAddress.value);

//auto buy
const autoBuyId = ref<number | null>(null);
const transactionCheckVal = ref<string>("");

async function autoBuy() {
  const autoBuy = await checkAndCreateAutoBuy(autoBuyStoreConfig);
  if (!autoBuy?.status) {
    currentAutoBuyStatus.title = "Error";
    return;
  }

  if (props.promoCode) {
    await tracker.logEvent(TrackerEvent.PROMOCODE_ACTIVATE, {
      code: props.promoCode,
    });

    await tracker.setUserProperty(TrackerEvent.PROMOCODE_USED, props.promoCode);
  }

  autoBuyId.value = autoBuy.data?.id ?? null;

  autoBuyCheckTimer.value = checkAutoBuyTimer(
    autoBuy.data?.id!,
    (status: TAutoBuyStatusCode) => {
      switch (status) {
        case 20:
          clearInterval(autoBuyCheckTimer.value);
          currentAutoBuyStatus.title = "Payments gone";
          router.push("/app/dashboard");
          break;
        case 30:
          clearInterval(autoBuyCheckTimer.value);
          break;
      }
    }
  );

  transactionCheckTimer.value = setInterval(async () => {
    const res = await checkAutoBuyTransaction(currentAccountAddress.value);
    if (!res.status) {
      clearInterval(transactionCheckTimer.value);
      return;
    }
    transactionCheckVal.value = res.data;
    if (transactionCheckVal.value !== res.data) {
      clearInterval(transactionCheckTimer.value);
      currentAutoBuyStatus.title = "In progress";
    }
  }, 3000);
}

autoBuy();

//copy address to buffer
const isCopiedAddress = ref(false);
const copyAddress = async (content: string) => {
  await copyToClipBoard(content);
  isCopiedAddress.value = true;
  setTimeout(() => (isCopiedAddress.value = false), 500);
};

//watch select value
watch(
  () => accountOptionValue,
  () => {
    currentAccount.value = accounts.findAccountByISO(
      accountOptionValue.value.isoCode,
      accountsWithAddress!
    );
    generateQrCodeURL(currentAccountAddress.value);
  },
  { deep: true }
);

getUserFees();

//unmounted hook
onUnmounted(async () => {
  await checkAndDeleteAutoBay();
  autoBuyCheckTimer.value ? clearInterval(autoBuyCheckTimer.value) : null;
  transactionCheckTimer.value
    ? clearInterval(transactionCheckTimer.value)
    : null;
});
</script>
<template>
  <div class="mx-auto max-w-[460px] font-granate p-1 flex flex-col gap-10">
    <template
      v-if="
        currentAutoBuyStatus.title !== 'In progress' &&
        ExperimentsService.getStatus('activateExperiment') !== '2'
      ">
      <div class="mt-2">
        <h2 class="text-neutral-900 font-medium text-2xl mb-3">
          {{ $t("createCard.payments.methodOfPayment") }}
        </h2>
        <v-select
          v-model="accountOptionValue"
          :options="accountSelectOptions"
          label="label">
          <template #selected-option="{ label, icon }">
            <div class="flex items-center w-full gap-3 font-medium">
              <span class="text-greyscale-600">
                <DynamicIcon
                  path="gateway"
                  :name="icon"
                  class="w-6 h-6" />
              </span>
              <span>
                {{ label }}
              </span>
            </div>
          </template>
          <template #option="{ label, icon }">
            <div class="flex items-center w-full gap-3 font-medium">
              <span class="text-greyscale-600">
                <DynamicIcon
                  path="gateway"
                  :name="icon"
                  class="w-6 h-6" />
              </span>
              <span>
                {{ label }}
              </span>
            </div>
          </template>
        </v-select>
      </div>
    </template>
    <!--    select block -->

    <!--    qr code and status payment block -->
    <div>
      <div class="">
        <h2 class="text-neutral-900 font-medium text-2xl mb-3">
          Top up {{ accountOptionValue.label }}
        </h2>
        <!--      qr code block -->
        <div class="p-4 bg-neutral-100 rounded-[6px] flex items-start gap-4">
          <!--        qr-code -->
          <div class="w-[80px] flex-shrink-0">
            <img
              :src="qrCodeURL"
              alt=""
              class="w-full h-[80px]" />
          </div>
          <!--        wallet data -->
          <div class="w-full">
            <div class="-mt-1.5">
              <span class="text-sm font-medium text-neutral-500 leading-none">{{
                $t("createCard.payments.network")
              }}</span>
              <p class="text-base leading-none">
                {{ accountOptionValue.isoCode }}
              </p>
            </div>
            <div class="mt-2">
              <span class="text-sm font-medium text-neutral-500">
                {{ $t("createCard.payments.walletAdress") }}
              </span>
              <div class="flex items-center justify-between w-full">
                <p
                  class="break-words leading-none"
                  :class="$style['text-address']">
                  {{ currentAccount?.addresses[0]["address"] }}
                </p>
                <DynamicIcon
                  v-tooltip="{
                    content: isCopiedAddress ? $t('Copied') : $t('Copy'),
                  }"
                  name="copy"
                  class="w-[18px] h-auto cursor-pointer text-fg-primary"
                  @click="copyAddress(currentAccountAddress)" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--    status and amount block -->
      <div class="grid grid-cols-2 gap-2 mt-2">
        <div class="bg-neutral-100 p-4 rounded-[6px]">
          <span
            class="text-base text-neutral-500 leading-tight font-medium block">
            {{ $t("createCard.payments.requiredReplenishmentAmount") }}:
          </span>
          <p class="text-xl font-semibold mt-4">
            {{ exchangeAmount }} {{ accountOptionValue.isoCode }}
          </p>
        </div>

        <div class="bg-neutral-100 p-4 rounded-[6px]">
          <span
            class="text-base text-neutral-500 leading-tight font-medium block">
            {{ $t("createCard.payments.checkingBlockchainTransaction") }}:
          </span>
          <p
            v-if="currentAutoBuyStatus.title === 'Pending'"
            class="text-xl font-semibold mt-4"
            :class="$style.paymentPending">
            {{ $t("transactions-state-pending") }}
          </p>
          <p
            v-if="currentAutoBuyStatus.title === 'In progress'"
            class="text-xl font-semibold mt-4"
            :class="$style.paymentProgress">
            {{ $t("transactions-state-progress") }}
          </p>
          <p
            v-if="currentAutoBuyStatus.title === 'Payments gone'"
            class="text-xl font-semibold mt-4"
            :class="$style.paymentGone">
            {{ $t("transactions-state-finished") }}
          </p>
          <p
            v-if="currentAutoBuyStatus.title === 'Error'"
            class="text-xl font-semibold mt-4"
            :class="$style.paymentError">
            Error
          </p>
        </div>
      </div>
    </div>

    <!--    attention block -->
    <div class="bg-neutral-100 p-4 rounded">
      <h3 class="font-bold text-normal">
        {{ $t("createCard.payments.attention") }}
      </h3>

      <ul class="flex flex-col gap-2 mt-3 list-disc pl-5 text-normal">
        <li>
          {{ $t("createCard.payments.attention.anotherNetwork") }}
        </li>
        <li
          v-if="
            isNil(userFees.deposit_fee_usdt) ||
            Number(userFees.deposit_fee_usdt) > 0
          ">
          {{ $t("createCard.payments.network.depositServiceFee") }}
        </li>
        <li v-if="accountOptionValue.isoCode === 'BTC'">
          {{ $t("attention-deposit-with-btc-auto-buy") }}
        </li>
      </ul>
    </div>
  </div>
</template>
<style module lang="scss">
.paymentPending {
  color: #194bfb;
}

.paymentProgress {
  color: #ff7d00;
}

.paymentGone {
  color: #2fc060;
}

.paymentError {
  @apply text-red-600;
}

.text-address {
  width: auto;
  font-size: 11px;
}
</style>
