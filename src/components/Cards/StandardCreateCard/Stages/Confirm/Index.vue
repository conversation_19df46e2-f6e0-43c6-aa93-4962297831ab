<script lang="ts">
export default {
  name: "StandardCreateCardConfirmStage",
};
</script>
<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import { computed, ref, onMounted, toRef } from "vue";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { useI18n } from "vue-i18n";
import { UserService } from "@modules/services/user";
import type { TTransferState } from "@/components/Transaction/Latest/types";
import { useUserExchangeRates } from "@/composable/User/ExchangeRates";
import { ExceptionCause, ExceptionReason } from "@/config/Exception";
import { loggerConsole } from "@/helpers/logger/Logger";
import { CardService } from "@modules/services/card";
import SelectAccount from "@/components/ui/Select/SelectAccount.deprecated.vue";
import { useAxios } from "@/helpers";
import { TrackerEvent, useTracker } from "@/composable";
import DepositTotals from "@/components/Cards/CreateCardWithAutoBuy/Stages/Deposite/Totals/Index.vue";
import { AttachPromoCode } from "@/components/Widgets/PromoCode/helper";
import { useUserStore } from "@/stores/user";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { experiments } from "@/config/cards";

type Props = {
  stateForm: any;
  slug: string;
};

const props = defineProps<Props>();

const userStore = useUserStore();
const tracker = useTracker();

const isFacebookCardsExperiments = computed(() => {
  return (
    props.slug === "facebook-cards" &&
    userStore.user?.experiments?.fbdeposit_discount1
  );
});

const feeTopUp = computed(() => {
  const feeTopUpTariffPercent = Number(selectedTariff.value?.fee_topup || "0");
  if (isFacebookCardsExperiments.value) {
    const startBalance = Number(cardForIssue.value?.start_balance || "0");
    const discountFeeTopUpPercent = Number(
      experiments?.facebookCards?.discountFeeTopUpPercent || "0"
    );
    const discountFeeTopUpFromUsd = Number(
      experiments?.facebookCards?.discountFeeTopUpFromUsd || "0"
    );
    if (
      startBalance &&
      discountFeeTopUpPercent &&
      startBalance >= discountFeeTopUpFromUsd
    ) {
      return feeTopUpTariffPercent - discountFeeTopUpPercent;
    }
  }
  return feeTopUpTariffPercent;
});

// const errorToIssueState: any = toRef<any>(() => props.stateForm?.errorToIssue);
// const errorToIssue = ref<string>(errorToIssueState.value ?? "");

const cardForIssueState: any = toRef<any>(() => props.stateForm?.cardForIssue);

const cardForIssue = ref<any>({
  bin: cardForIssueState.value?.bin ?? "",
  system: cardForIssueState.value?.system ?? undefined,
  type: cardForIssueState.value?.type ?? "",
  start_balance: cardForIssueState.value?.start_balance ?? "50",
  minValue: cardForIssueState.value?.minValue ?? "50",
  description: cardForIssueState.value?.description ?? "",
  account: cardForIssueState.value?.account ?? "",
  address: cardForIssueState.value?.address ?? "",
  id: cardForIssueState.value?.id ?? "",
  count: cardForIssueState.value?.count ?? 1,
});

const promoCodeDataState = toRef<any>(() => props.stateForm?.promoCodeData);
const promoCodeData = ref<any>({
  data: promoCodeDataState.value?.data ?? undefined,
  list: promoCodeDataState.value?.list ?? [],
  code: promoCodeDataState.value?.code ?? "",
});

const percentDiscountCostCardPromoCode = computed(() => {
  if (!promoCodeData.value.code) return 0;
  const data = promoCodeData?.value.data;
  if (data?.fields) {
    return Number(data.fields?.card_buy_discount_percent || 0);
  } else {
    return 0;
  }
});

const autoRefillFormState: any = toRef<any>(
  () => props.stateForm?.autoRefillForm
);

const autoRefillForm = ref<any>({
  minimumBalance: autoRefillFormState.value?.minimumBalance ?? "50",
  amountRefill: autoRefillFormState.value?.amountRefill ?? "50",
  isActive: autoRefillFormState.value?.isActive ?? false,
});

const accountIdState: any = toRef<any>(() => props.stateForm?.accountId);
const tState = ref<TTransferState>({
  type: props.stateForm?.type ?? "",
  step: props.stateForm?.step ?? 1,
  toId: -1,
  toDirection: "",
  toAmount: "0",
  fromId: accountIdState.value ?? -1,
  fromDirection: "account",
  fromAmount: "0",
  isSubmit: false,
  isConfirm: false,
  error: {},
});

const resultCard = ref<any>({
  description: "",
  id: 0,
  mask: "0",
});

const isLoading = ref<boolean>(false);

const emit = defineEmits([
  "confirmHandler",
  "confirmErrorHandler",
  "kyc-card-limit",
  "kyc-deposit-limit",
]);

const { accounts } = useUserAccounts();

const userTariff = ref<Array<any>>([]);
const userSpecial = ref<any>(null);
const logger = loggerConsole();

// data
const { t } = useI18n();
const { data: rates } = useUserExchangeRates();

const loadUserTariff = async () => {
  const response = await UserService.tariff();
  const special = await UserService.special();

  if (!response.status) {
    useCallToast({
      title: t("toast.error"),
      body: response.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    logger.error(ExceptionCause.USER_TARIFF, ExceptionReason.FAILED_LOAD);
  }

  userSpecial.value = special?.data;
  userTariff.value = response?.data || [];
};

const selectedTariff = computed(() =>
  userTariff.value.find((tariff: any) => tariff.slug === props.slug)
);

onMounted(async () => {
  await loadUserTariff();
});

const getMultiBuyBody = () => ({
  account_id: cardForIssue.value.account_id || cardForIssue.value.account.id,
  type: cardForIssue.value.type,
  start_balance: String(cardForIssue.value.start_balance),
  description: cardForIssue.value.description,
  system: cardForIssue.value.system,
  bin: cardForIssue.value.bin,
  count: cardForIssue.value.count,
  with_error_data: true,
});

interface IMultiBuyCheck {
  status: boolean;
  type?: string;
}
const multiBuyCheck = async (): Promise<IMultiBuyCheck> => {
  const checkResult = await CardService.multiBuyCheck(getMultiBuyBody());
  let data = checkResult?.data;
  if (checkResult?.status) {
    if (data?.success) {
      return { status: true };
    } else {
      return { status: false, type: data?.type };
    }
  } else {
    data = checkResult?.axiosResponse?.data;
    return { status: false, type: data?.type };
  }
};

// Multi Buy Request
const multiBuyRequest = async () => {
  try {
    const body = getMultiBuyBody();
    await CardService.multiBuy(body);

    tracker.logEvent(TrackerEvent.CARD_ISSUED, {
      slug: body.type,
      count: body.count,
    });

    emit("confirmHandler", {
      startBalance: cardForIssue.value.start_balance,
      selectedTariff: selectedTariff.value,
      countCards: cardForIssue.value.count,
      percentDiscountCostCardPromoCode: percentDiscountCostCardPromoCode.value,
      addRate: cardForIssue.value.account?._currency?.iso_code,
    });
  } catch (ex: any) {
    if (ex.response !== undefined) {
      const type = ex?.response?.data?.type;
      emit("confirmErrorHandler", { error: t(`errors.card.create.${type}`) });
      console.error("CreateCard/Form->multiBuyRequest error handled: ", ex);
      return;
    }
    //errorToIssue.value = ex.toString();
    emit("confirmErrorHandler", { error: t("cards.create.error.common") });
  }
};

const createRefill = async (id: number) => {
  const response = await CardService.createRefill({
    card_id: id,
    minimum_balance: autoRefillForm.value.minimumBalance,
    amount_refill: autoRefillForm.value.amountRefill,
  });

  if (!response.status) {
    console.error("CreateCard/Form->createRefill error handled: ", response);
    return;
  }
};

const issueCard = async () => {
  try {
    isLoading.value = true;

    cardForIssue.value.account_id =
      cardForIssue.value.account.id ?? accountIdState.value;

    // if cards count > 1
    if (Number(cardForIssue.value.count) > 1) {
      const multiBuyCheckReq: IMultiBuyCheck = await multiBuyCheck();
      if (multiBuyCheckReq.status) {
        await multiBuyRequest();
      } else {
        emit("confirmErrorHandler", {
          error: t(`errors.card.create.${multiBuyCheckReq.type}`),
        });
      }
    } else {
      try {
        if (promoCodeData.value.code) {
          const attachPromoCode = new AttachPromoCode(promoCodeData.value.code);
          const resAttach = await attachPromoCode.attach();
          if (!resAttach.status) {
            emit("confirmErrorHandler", {
              error: t("cards.create.error.promocode_server_error"),
            });
            return;
          }
        }

        const res = await useAxios().post("/card/buy", {
          account_id: cardForIssue.value.account.id ?? accountIdState.value,
          type: cardForIssue.value.type,
          start_balance: String(cardForIssue.value.start_balance),
          description: cardForIssue.value.description,
          system: cardForIssue.value.system
            ? Number(cardForIssue.value.system)
            : undefined,
          bin: Number(cardForIssue.value.bin),
          with_error_data: true,
        });

        resultCard.value = res.data.data;

        // Create auto refill
        if (resultCard.value.id && autoRefillForm.value.isActive) {
          await createRefill(resultCard.value.id);
        }

        emit("confirmHandler", {
          startBalance: cardForIssue.value.start_balance,
          selectedTariff: selectedTariff.value,
          countCards: cardForIssue.value.count,
          percentDiscountCostCardPromoCode:
            percentDiscountCostCardPromoCode.value,
          addRate: cardForIssue.value.account?._currency?.iso_code,
        });

        tracker.logEvent(TrackerEvent.CARD_ISSUED, {
          slug: cardForIssue.value.type,
        });
      } catch (ex: any) {
        if (ex.response !== undefined) {
          const type: string | undefined = ex.response?.data?.type;

          //Check Kyc Limit
          if (type === "KYC_LIMIT") {
            emit("confirmErrorHandler", {
              error: t("cards.create.error.kyc_limit_user_has_one_card"),
            });
            emit("kyc-card-limit");
          } else if (
            ["CARD_DEPOSIT_LIMIT", "card_deposit_limit"].includes(
              ex.response.data.field
            )
          ) {
            emit("kyc-deposit-limit");
          } else if (type) {
            emit("confirmErrorHandler", {
              error: t(`errors.card.create.${type}`),
            });
          } else {
            emit("confirmErrorHandler", {
              error: t(`errors.universal-request-error`),
            });
          }
        } else {
          emit("confirmErrorHandler", {
            error: t("cards.create.error.common"),
          });
        }
      } finally {
        //loading.value = false;
      }
    }

    //loading.value = false;
  } catch (e) {
    emit("confirmErrorHandler", { error: t("cards.create.error.common") });
  }
};
</script>
<template>
  <div :class="$style.root">
    <!-- Confirm transfer -->
    <div class="flex flex-none items-start w-full">
      <span :class="$style['step2__title']">{{
        $t("Confirm your transfer")
      }}</span>
    </div>

    <div class="flex flex-none items-start w-full mt-10">
      <span :class="$style['step2__sub-title']">
        {{ $t("Transfer from") }}</span
      >
    </div>

    <div class="flex flex-none flex-col w-full mt-3">
      <SelectAccount
        :disabled="true"
        :hide-chevron="true"
        :value="tState.fromId"
        :options="accounts"
        :rates="rates"
        backdrop
        searchable
        :error="tState.error.selectFrom" />
    </div>

    <!-- Total sum -->
    <div class="flex flex-none mt-4">
      <DepositTotals
        v-if="cardForIssue"
        ref="depositTotals"
        :balance="String(cardForIssue.start_balance)"
        :fee-top-up="feeTopUp"
        :fake-tariff="selectedTariff"
        :user-tariff="selectedTariff"
        :add-rate="cardForIssue.account?._currency?.iso_code"
        :count-cards="cardForIssue.count"
        :percent-discount-cost-card-promo-code="
          percentDiscountCostCardPromoCode
        "
        class="w-full" />
    </div>

    <!-- Send button -->
    <UiButton
      :title="$t('issue_a_card')"
      class="w-full mt-10"
      data-cy="order_button"
      :disabled="isLoading"
      @click.prevent="issueCard" />
  </div>
</template>
<style lang="scss" module>
.root {
  @apply max-w-[440px] mx-auto px-3;
}

.step2 {
  &__title {
    color: var(--sys-color-text-primary, #313438);
    font-family: ALS Hauss VF;
    font-size: 24px;
    font-style: normal;
    font-weight: 550;
    line-height: 20px;
  }

  &__sub-title {
    color: var(--sys-color-text-primary, #313438);
    font-family: ALS Hauss VF;
    font-size: 20px;
    font-style: normal;
    font-weight: 550;
    line-height: 20px;
  }

  &__card {
    display: flex;
    height: 60px;
    padding: var(--spacing-0, 0px) 12px;
    align-items: center;
    gap: 8px;
    align-self: stretch;

    border-radius: 6px;
    border: 1.5px solid var(--sys-color-input-border-static, #e8e9eb);
    background: var(--sys-color-input-bg, #fff);
  }
}
</style>
