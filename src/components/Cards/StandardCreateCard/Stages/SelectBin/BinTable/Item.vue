<script lang="ts" setup>
import UiButton from "@/components/ui/Button/Button.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useI18n } from "vue-i18n";
import type { TCardTariff } from "@/types/user/user.types";
import { useRouter } from "vue-router";
import { computed } from "vue";
import {
  useUserSpecialCondition,
  useTracker,
  TrackerEvent,
} from "@/composable";
import { Tooltip } from "floating-vue";

defineOptions({ name: "StandardCreateCardSelectBinItem" });

interface TBinInfo {
  bin: string;
  gateway: "mastercard" | "visa";
  secure: boolean;
  autoSecure?: boolean;
  private: boolean;
  featured: boolean;
  transferFee: string;
  depositFee: string;
  cashback: string;
  monthlyPayment: string;
  recommended: any[];
  slug?: string;
  default?: {
    transferFee?: string;
    depositFee?: string;
    cashback?: string;
    monthlyPayment?: string;
  };
  tariffs?: string[];
  under_maintenance: boolean;
}

interface Props {
  template: string;
  data: TBinInfo;
  tariff: TCardTariff;
  subscription: boolean;
  cashback: string;
  subscriptionLevel?: string;
  currentMainTariffName?: string;
}
const router = useRouter();
const tracker = useTracker();
const { t } = useI18n();
const { hasSpecialCondition } = useUserSpecialCondition();

const props = defineProps<Props>();
const emit = defineEmits(["onSelectBin", "improveTariff"]);

const onSelectHandler = async () => {
  if (!props.data.private) {
    emit("onSelectBin", props.data.bin, props.data.slug);
  } else {
    await router.push("/app/subscription/promo");
    await tracker.logEvent(TrackerEvent.BUTTON, {
      page: "/app/create",
      page_version: 0,
      name: "Private",
      version: 0,
      location: "table",
    });
  }
};

const isAuto3ds = computed(() => {
  return ["542723", "517746"].includes(props.data.bin);
});

const availableAtHigherTariff = computed(() => {
  return (
    !!props.subscriptionLevel &&
    !!props.data.tariffs &&
    !props.data.tariffs.includes(props.subscriptionLevel)
  );
});

const improveTariff = () => {
  emit("improveTariff", props.data.bin, props.data.tariffs);
};
</script>

<template>
  <div
    :class="[$style.root, template, { 'bg-bg-orange-light': data.featured }]">
    <!--  BIN  -->
    <template v-if="data?.under_maintenance">
      <Tooltip
        placement="bottom-start"
        :triggers="['hover']">
        <a>
          <div
            v-tooltip="{
              content:
                data.featured && data.bin === '542723'
                  ? $t('cards-create.exclusive-bin')
                  : null,
            }"
            class="flex items-center justify-between gap-2 px-2 py-2 md:py-3">
            <span class="visible md:hidden text-sm text-fg-primary">
              {{ t("label.bin") }}
            </span>
            <span
              class="flex items-center gap-1 rounded px-1.5 py-px bg-bg-orange-light">
              <DynamicIcon
                v-if="data.featured"
                name="fire"
                class="h-4 w-4 text-core-red-500" />

              {{ data.bin }}

              <DynamicIcon
                name="warning_circle"
                class="h-4 w-4 text-fg-orange" />
            </span>
          </div>
        </a>
        <template #popper>
          <div class="max-w-[260px]">
            <span class="text-4 text-white">{{
              $t("common.technical-work.title")
            }}</span>
            <p class="text-3.5 text-fg-tertiary">
              {{ $t("common.technical-work.descriptions") }}
            </p>
          </div>
        </template>
      </Tooltip>
    </template>
    <div
      v-else
      v-tooltip="{
        content:
          data.featured && data.bin === '542723'
            ? $t('cards-create.exclusive-bin')
            : null,
      }"
      class="flex items-center justify-between gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-fg-primary">
        {{ t("label.bin") }}
      </span>
      <span class="flex items-center gap-1 rounded px-1.5 py-px bg-neutral-150">
        <DynamicIcon
          v-if="data.featured"
          name="fire"
          class="h-4 w-4 text-core-red-500" />
        {{ data.bin }}
      </span>
    </div>

    <!--  Payment System   -->
    <div class="flex items-center justify-between gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("label.paymentSystem") }}
      </span>

      <span class="bg-neutral-150 rounded-[6px] px-[6px] py-1 w-fit">
        <DynamicIcon
          :name="data.bin?.startsWith('4') ? 'visa' : 'mastercard'"
          path="gateway"
          class="h-3 w-6" />
      </span>
    </div>

    <!--  Recommended for  -->
    <div
      class="flex items-center justify-between md:justify-start gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("label.recommended") }}
      </span>
      <ul class="flex items-center gap-1">
        <li
          v-for="(item, index) in data.recommended"
          :key="index">
          <DynamicIcon
            :name="`social-circle-${item}`"
            class="w-5 h-5 min-w-[20px]" />
        </li>
      </ul>
    </div>

    <!--  Secure  -->
    <div class="flex items-center justify-center gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("label.secure") }}
      </span>

      <DynamicIcon
        :name="data.secure || isAuto3ds ? 'check' : 'close'"
        class="h-6 w-6"
        :class="
          data.secure || isAuto3ds ? 'text-neutral-800' : 'text-neutral-600'
        " />
      <span v-if="data.autoSecure || isAuto3ds">Auto</span>
    </div>

    <!--  Transaction fee  -->
    <div
      class="flex items-center justify-between md:justify-end gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("createFirstCard.transactionFee") }}
      </span>
      <div class="inline">
        <span
          v-if="data.default?.transferFee"
          :class="[$style['text-fill-right']]"
          class="mr-2 text-[13px] text-neutral-400 line-through">
          {{ data.default.transferFee }}
        </span>
        <span :class="[$style.text, $style['text-fill-right']]">
          {{ Number(tariff?.fee_transaction_amount) }} $
        </span>
      </div>
    </div>

    <!--  Deposit fee  -->
    <div
      class="flex items-center justify-between md:justify-end gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("createFirstCard.depositeFee") }}
      </span>
      <div class="inline">
        <span
          v-if="data.default?.depositFee"
          :class="[$style['text-fill-right']]"
          class="mr-2 text-[13px] text-neutral-400 line-through">
          {{ Number(data.default?.depositFee) }} %
        </span>
        <span :class="[$style.text, $style['text-fill-right']]">
          {{ Number(tariff?.fee_topup) }} %
        </span>
      </div>
    </div>

    <!--  Cashback  -->
    <div
      class="flex items-center justify-between md:justify-end gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("cashback.title") }}
      </span>
      <div class="inline">
        <span
          v-if="subscription"
          :class="[$style['text-fill-right']]"
          class="mr-2 text-[13px] text-neutral-400 line-through">
          0 %
        </span>
        <span :class="[$style.text, $style['text-fill-right']]">
          {{ cashback }} %
        </span>
      </div>
    </div>

    <!--  Month Payment  -->
    <div
      class="flex items-center justify-between md:justify-end gap-2 px-2 py-2 md:py-3">
      <span class="visible md:hidden text-sm text-neutral-500">
        {{ t("createFirstCard.monthlyPayment") }}
      </span>
      <template v-if="data.tariffs === undefined || data.tariffs.length === 0">
        <span
          v-if="data.default?.monthlyPayment"
          :class="[$style['text-fill-right']]"
          class="mr-2 text-[13px] text-neutral-400 line-through">
          {{ Number(data.default?.monthlyPayment) }} $
        </span>
        <div class="flex flex-none">
          <span :class="[$style.text, $style['text-fill-right']]">
            {{ tariff?.card_price }} $
          </span>
        </div>
      </template>
      <template
        v-else-if="
          !subscriptionLevel || !data.tariffs.includes(subscriptionLevel)
        ">
        {{
          data.tariffs[0].toUpperCase() === "S"
            ? t("createFirstCard.bins.startingFromPrivate")
            : t("createFirstCard.bins.startingFrom") +
              " " +
              data.tariffs[0].toUpperCase()
        }}
      </template>
      <template v-else-if="data.tariffs.includes(subscriptionLevel)">
        {{ tariff?.card_price }} $
      </template>
    </div>

    <!--  Select Button  -->
    <div
      class="flex items-center justify-between md:justify-end gap-2 px-2 py-2 md:py-3">
      <span :class="[$style.text, $style['text-fill-right']]">
        <UIButton
          v-if="hasSpecialCondition && availableAtHigherTariff"
          class="w-full"
          color="black"
          size="xs"
          @click="improveTariff">
          {{ t("btn.go") }}
        </UIButton>
        <UiButton
          v-else
          :title="data.private ? 'Private' : t('Order')"
          size="normal"
          :type="data.private ? 'primary' : 'light'"
          :class="$style.button"
          :disabled="
            !!subscriptionLevel &&
            !!data.tariffs &&
            !data.tariffs.includes(subscriptionLevel) &&
            !props.currentMainTariffName?.startsWith('Enterprise')
          "
          @click="onSelectHandler" />
      </span>
    </div>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply grid relative text-[13px];

  & > div:nth-child(n + 2) {
    @apply md:border-l md:border-transparent;
  }
}

.text {
  @apply text-3.5 text-neutral-800 text-right md:text-left;
}

.text-fill-right {
  width: 100%;
  text-align: right;
}

.monospace {
  @apply font-monospace;
}

.button {
  @apply h-6 rounded font-medium w-full;
}
</style>
