<script lang="ts" setup>
import { Skeletor } from "vue-skeletor";
import {
  computed,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRef,
  watch,
} from "vue";
import type {
  IAutoBayStoreDto,
  TAutoBuyStatusCode,
} from "@modules/services/autoBuy";
import { useI18n } from "vue-i18n";
import { UserService } from "@modules/services/user";
import { useUserExchangeRates } from "@/composable/User/ExchangeRates";
import { ExceptionCause, ExceptionReason } from "@/config/Exception";
import { loggerConsole } from "@/helpers/logger/Logger";
import { TrackerEvent, useTracker } from "@/composable";
import { useUserStore } from "@/stores/user";
import CardIssueSummary from "@/components/CardIssueSummary/CardIssueSummary.vue";
import type {
  TCardTariff,
  TUserSpecial,
  TUserTariff,
} from "@/types/user/user.types";
import type { TCardTariffSlug } from "@/composable/Tariff";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { type TIsoCode, useAccount } from "@/composable/useAccounts/";
import QrCode from "qrcode";
import { useRouter } from "vue-router";

import {
  checkAndCreateAutoBuy,
  checkAndDeleteAutoBay,
  checkAutoBuyTimer,
  checkAutoBuyTransaction,
  type IAutoBuyStatus,
} from "@/composable/CardAutoBuy/";

import { conversionCurrency } from "@/composable/useAccounts";
import { RouteName } from "@/constants/route_name";
import ExperimentsService from "@/services/ExperimentsService";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UISelect from "@/components/ui/UISelect/UISelect.vue";
import UITransition from "@/components/ui/UITransition.vue";
import type { TUISelectOption } from "@/components/ui/UISelect/types";
import { useClipboard } from "@vueuse/core";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { calcTopUpFee } from "@/helpers/calcTopUpFee";
import { isNil } from "lodash";

defineOptions({ name: "CreateCardUltimaAutoBuyConfirm" });

const { copy } = useClipboard();
const { t } = useI18n();
const tracker = useTracker();

const copyValue = (value: string) => {
  copy(value);
  useCallToast({
    title: t("cards.copied"),
  });
};

const router = useRouter();

const userStore = useUserStore();

interface Props {
  stateForm: any;
  slug: TCardTariffSlug;
}

const props = defineProps<Props>();

const cardForIssueState: any = toRef<any>(() => props.stateForm?.cardForIssue);
const cardForIssue = ref<any>({
  system: cardForIssueState.value?.system ?? 0,
  type: cardForIssueState.value?.type ?? "",
  start_balance: cardForIssueState.value?.start_balance ?? "50",
  minValue: cardForIssueState.value?.minValue ?? "50",
  description: cardForIssueState.value?.description ?? "",
  account: cardForIssueState.value?.account ?? "",
  address: cardForIssueState.value?.address ?? "",
  id: cardForIssueState.value?.id ?? "",
  count: cardForIssueState.value?.count ?? 1,
});

const promoCodeDataState = toRef<any>(() => props.stateForm?.promoCodeData);
const promoCodeData = ref<any>({
  data: promoCodeDataState.value?.data ?? undefined,
  list: promoCodeDataState.value?.list ?? [],
  code: promoCodeDataState.value?.code ?? "",
});

const percentDiscountCostCardPromoCode = computed(() => {
  if (!promoCodeData.value.code) return 0;
  const data = promoCodeData?.value.data;
  if (data?.fields) {
    return Number(data.fields?.card_buy_discount_percent || 0);
  } else {
    return 0;
  }
});

const userTariff = ref<TUserTariff>([]);
/**
 * @todo seems to be useless ref
 */
const userSpecial = ref<TUserSpecial>();
const logger = loggerConsole();

// data

const { data: rates } = useUserExchangeRates();

const loadUserTariff = async () => {
  const response = await UserService.tariff();
  const special = await UserService.special();

  if (!response.status) {
    useCallToast({
      title: t("toast.error"),
      body: response.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    logger.error(ExceptionCause.USER_TARIFF, ExceptionReason.FAILED_LOAD);
  }

  userSpecial.value = special?.data;
  userTariff.value = response?.data || [];
};

const isSecure = ref<boolean>(props.slug.includes("-3ds"));

const defaultSelectedTariff = computed(() => {
  const slug = isSecure.value ? props.slug.replace("-3ds", "") : props.slug;
  return userTariff.value.find((tariff: TCardTariff) => tariff.slug === slug);
});

const selectedTariff = computed(() =>
  userTariff.value.find((tariff: TCardTariff) => tariff.slug === props.slug)
);

const comissionUsdDepositingBtc = 10;

//CardIssueSummary data
const totalPaymentUsd = computed<number | undefined>(() => {
  const startBalance = Number(cardForIssue.value?.start_balance ?? "0");
  const feeTopUpPercent = Number(selectedTariff.value?.fee_topup ?? "0");
  const feeTopUp = calcTopUpFee(startBalance, feeTopUpPercent, 1);

  let total = getCardPrice() + startBalance + feeTopUp;
  if (accountOptionValue.value === "BTC") {
    total *= 1.03; //To correct BTC volatility
    if (total < 1000) {
      total += comissionUsdDepositingBtc;
    }
  }

  return total;
});
const getCardPrice = (): number => {
  let cardPrice = Number(selectedTariff.value?.card_price) || 0;
  if (!cardPrice) return 0;
  return calcDiscount(cardPrice, percentDiscountCostCardPromoCode.value);
};

const calcDiscount = (price: number, discountPercent: number) => {
  return price - (price * discountPercent) / 100;
};

const ultimaTariffName = computed(() => {
  const names: { [key: string]: string } = {
    "ultima-weekly": t("cards.plan-weekly"),
    "ultima-annually": t("cards.plan-annually"),
    ultima: t("cards.plan-monthly"),
  };
  const tariffSlug =
    defaultSelectedTariff.value?.slug.replace("-3ds", "") || "";
  const plan = t("cards.plan");
  return `${plan} ${names[tariffSlug]}`;
});

const tariffMonthlyPayment = computed(() => {
  return parseFloat(defaultSelectedTariff.value?.card_price || "0").toFixed(2);
});

const tariffMonthlyPaymentWithDiscount = computed(() => {
  if (!percentDiscountCostCardPromoCode.value) return;
  const price = getCardPrice();
  return parseFloat(String(price)).toFixed(2);
});

const cardStartBalance = computed(() => {
  return parseFloat(cardForIssue.value?.start_balance).toFixed(2) || "0.00";
});

const selectedTariffFeeTopUp = computed(() => {
  const feeTopUpPercent = Number(selectedTariff.value?.fee_topup ?? "0");
  return feeTopUpPercent > 0 ? feeTopUpPercent.toFixed(1) : null;
});

const cardTotal = computed(() => {
  if (!accountOptionValue.value) return "0.00";
  if (!totalPaymentUsd.value) return "0.00";
  const value = conversionCurrency(
    totalPaymentUsd.value || 0,
    accountOptionValue.value,
    rates.value
  );
  if (accountOptionValue.value === "BTC") {
    return Number(value).toFixed(8);
  } else {
    return Number(value).toFixed(2);
  }
});

const securityAdditionalCost = computed(() => {
  if (!isSecure.value) return;
  return (
    parseFloat(selectedTariff.value?.card_price || "0") -
    parseFloat(defaultSelectedTariff.value?.card_price || "0")
  ).toFixed(2);
});

const exchangeRates = computed(() => {
  const isoCode = accountOptionValue.value;
  if (!isoCode) return 1;
  if (isoCode === IsoCodeNames.USD) return 1;
  if (!rates.value || !rates.value[isoCode]) return 1;
  return rates.value[isoCode][IsoCodeNames.USD];
});

const exchangeRate = computed<string | undefined>(() => {
  if (exchangeRates.value === undefined) return;
  return parseFloat(exchangeRates.value).toFixed(2) || "0";
});

onMounted(async () => {
  await loadUserTariff();
});

/* QR-CODE PART */

const networkByIsoCodes = {
  USDT: "Tether (TRC20)",
  BTC: "BTC",
  USD: "",
  EUR: "",
};
const getNetworkByIsoCodes = (
  isoCode: TIsoCode | string | number | undefined
) => {
  if (!isoCode) return "";
  return networkByIsoCodes[String(isoCode) as TIsoCode];
};

const accountIcons = {
  BTC: "btc",
  USDT: "usdt-trc20",
  USD: "",
  EUR: "",
};
const getAccountIcon = (isoCode: TIsoCode | string | number | undefined) => {
  if (!isoCode) return "";
  return accountIcons[String(isoCode) as TIsoCode];
};

//select

const accountOptionValue = ref<TIsoCode>("USDT");
const accountSelectOptions: TUISelectOption[] = [
  {
    label: networkByIsoCodes["USDT"],
    value: "USDT",
  },
  {
    label: networkByIsoCodes["BTC"],
    value: "BTC",
  },
];

//auto buy status
const currentAutoBuyStatus = reactive<IAutoBuyStatus>({
  status: 10,
  title: "Pending",
});

//auto buy payload config
const autoBuyStoreConfig = reactive<IAutoBayStoreDto>({
  type: props.slug,
  start_balance: String(cardForIssue.value.start_balance),
  code: promoCodeData.value.code || "",
  system: cardForIssue.value.system,
});

//timers
const autoBuyCheckTimer = ref<any>(null);
const transactionCheckTimer = ref<any>(null);

//account data
const accounts = useAccount();
const accountsWithAddress = ref();
const currentAccount = ref();

const init = async () => {
  accountsWithAddress.value = await accounts.getAllWithAddress();
  currentAccount.value = accounts.findAccountByISO(
    "USDT",
    accountsWithAddress.value!
  );
  autoBuyStoreConfig.account_id = currentAccount.value?.id;
  generateQrCodeURL(currentAccountAddress.value);
};

init();

//current account address
const currentAccountAddress = computed(
  () => currentAccount.value?.addresses[0]["address"]!
);

//qr code generate
const qrCodeURL = ref("");
const generateQrCodeURL = async (address: string) => {
  if (!address) return;
  qrCodeURL.value = await QrCode.toDataURL(address);
};

//auto buy
const autoBuyId = ref<number | null>(null);
const transactionCheckVal = ref<string>("");

const autoBuy = async () => {
  const autoBuy = await checkAndCreateAutoBuy(autoBuyStoreConfig);

  if (!autoBuy?.status) {
    currentAutoBuyStatus.title = "Error";
    return;
  }

  if (promoCodeData.value.code) {
    await tracker.logEvent(TrackerEvent.PROMOCODE_ACTIVATE, {
      code: promoCodeData.value.code,
    });

    await tracker.setUserProperty(
      TrackerEvent.PROMOCODE_USED,
      promoCodeData.value.code
    );
  }

  autoBuyId.value = autoBuy.data?.id ?? null;

  autoBuyCheckTimer.value = checkAutoBuyTimer(
    autoBuy.data?.id!,
    (status: TAutoBuyStatusCode) => {
      switch (status) {
        case 20:
          clearInterval(autoBuyCheckTimer.value);
          currentAutoBuyStatus.title = "Payments gone";
          router.push({ name: RouteName.DASHBOARD });
          break;
        case 30:
          clearInterval(autoBuyCheckTimer.value);
          break;
      }
    }
  );

  transactionCheckTimer.value = setInterval(async () => {
    const res = await checkAutoBuyTransaction(currentAccountAddress.value);
    if (!res.status) {
      clearInterval(transactionCheckTimer.value);
      return;
    }
    transactionCheckVal.value = res.data;
    if (transactionCheckVal.value !== res.data) {
      clearInterval(transactionCheckTimer.value);
      currentAutoBuyStatus.title = "In progress";
    }
  }, 3000);
};

autoBuy();
userStore.getUserFees();

//watch select value
watch(
  () => accountOptionValue,
  () => {
    currentAccount.value = accounts.findAccountByISO(
      accountOptionValue.value,
      accountsWithAddress.value!
    );
    generateQrCodeURL(currentAccountAddress.value);
  },
  { deep: true }
);

onUnmounted(async () => {
  await checkAndDeleteAutoBay();
  autoBuyCheckTimer.value ? clearInterval(autoBuyCheckTimer.value) : null;
  transactionCheckTimer.value
    ? clearInterval(transactionCheckTimer.value)
    : null;
});
/* QR-CODE PART END */
</script>

<template>
  <div class="flex flex-col gap-10 max-w-[27.5rem] mx-auto px-3">
    <!-- Confirm transfer -->

    <template
      v-if="
        currentAutoBuyStatus.title !== 'In progress' &&
        ExperimentsService.getStatus('activateExperiment') !== '2'
      ">
      <div>
        <div class="text-4 text-fg-secondary">
          {{ $t("createCard.payments.methodOfPayment") }}
        </div>
        <UISelect
          v-model="accountOptionValue"
          size="m"
          :cleared="false"
          :placeholder="$t('membersV2.filter.selectStatus')"
          :options="accountSelectOptions">
          <template #selectedOption="{ option }">
            <div
              class="flex items-center w-full gap-3 font-medium p-2 cursor-pointer">
              <span class="text-greyscale-600">
                <DynamicIcon
                  path="gateway"
                  :name="getAccountIcon(option?.value)"
                  class="w-6 h-6" />
              </span>
              <span>
                {{ getNetworkByIsoCodes(option?.value) }}
              </span>
            </div>
          </template>

          <template #option="{ option }">
            <div
              class="flex items-center w-full gap-3 font-medium p-4 cursor-pointer hover:bg-bg-level-1 transition-all">
              <span class="text-greyscale-600">
                <DynamicIcon
                  path="gateway"
                  :name="getAccountIcon(option?.value)"
                  class="w-6 h-6" />
              </span>
              <span>
                {{ getNetworkByIsoCodes(option?.value) }}
              </span>
            </div>
          </template>
        </UISelect>
      </div>
    </template>

    <UITransition>
      <Skeletor
        v-if="cardTotal === '0.00'"
        class="rounded"
        height="310"
        as="div" />
      <!-- Total sum -->
      <div
        v-else
        class="flex flex-none">
        <!-- Total -->
        <CardIssueSummary
          :total="`${cardTotal} ${accountOptionValue}`"
          :tariff-fee-top-up="selectedTariffFeeTopUp"
          :tariff-name="ultimaTariffName"
          :tariff-monthly-payment="tariffMonthlyPayment"
          :tariff-monthly-payment-with-discount="
            tariffMonthlyPaymentWithDiscount
          "
          :starting-balance-on-card="cardStartBalance"
          :account-iso-code="accountOptionValue"
          :security-additional-cost="securityAdditionalCost"
          :is-loading="!cardTotal"
          :exchange-rate="exchangeRate" />
      </div>
    </UITransition>

    <UITransition>
      <Skeletor
        v-if="!qrCodeURL"
        class="rounded"
        height="310"
        as="div" />
      <div
        v-else
        class="flex flex-col gap-2">
        <div class="flex flex-col p-4 gap-4 bg-bg-level-1 rounded">
          <div
            class="flex flex-none text-4.5 font-medium text-fg-primary leading-6">
            {{ $t("cards.transfer-funds") }}
          </div>
          <div class="flex flex-row gap-4">
            <div class="flex flex-none">
              <img
                :src="qrCodeURL"
                alt="Address"
                class="rounded w-[7.25rem] h-[7.25rem]" />
            </div>
            <div class="flex flex-auto flex-col justify-between">
              <div class="flex flex-none flex-col">
                <div class="flex flex-none text-fg-secondary text-4 leading-5">
                  {{ $t("cards.transfer-network") }}
                </div>
                <div class="flex flex-none text-fg-primary text-4 leading-5">
                  {{ getNetworkByIsoCodes(accountOptionValue) }}
                </div>
              </div>
              <div class="flex flex-none flex-col">
                <div class="text-fg-secondary text-4 leading-5">
                  {{ $t("cards.wallet-address") }}
                </div>
                <div
                  class="flex flex-none flex-row text-fg-primary text-4 leading-5 break-all">
                  <div class="flex flex-auto">
                    {{ currentAccountAddress }}
                  </div>
                  <div class="flex">
                    <DynamicIcon
                      v-tooltip="{ content: $t('Copy') }"
                      name="copy"
                      class="w-7 cursor-pointer text-fg-primary"
                      @click="copyValue(currentAccountAddress)" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="flex flex-none p-4 flex-row bg-bg-level-1 rounded items-center">
          <div class="flex flex-none mr-3 p-1.5 bg-bg-level-0 rounded-full">
            <DynamicIcon
              name="clock"
              class="w-6 h-6 text-fg-secondary" />
          </div>
          <div class="flex flex-auto flex-col">
            <div class="flex flex-none text-fg-secondary text-4 leading-5">
              {{ $t("cards.transaction-status") }}
            </div>
            <div class="flex flex-none text-4 leading-5 mt-0.5">
              <span
                v-if="currentAutoBuyStatus.title === 'Pending'"
                class="text-fg-blue">
                {{ $t("transactions-state-pending") }}
              </span>
              <span
                v-if="currentAutoBuyStatus.title === 'In progress'"
                class="text-fg-orange">
                {{ $t("transactions-state-progress") }}
              </span>
              <span
                v-if="currentAutoBuyStatus.title === 'Payments gone'"
                class="text-fg-green">
                {{ $t("transactions-state-finished") }}
              </span>
              <span
                v-if="currentAutoBuyStatus.title === 'Error'"
                class="text-red-600">
                Error
              </span>
            </div>
          </div>
        </div>
        <div
          v-if="accountOptionValue === 'USDT'"
          class="flex text-center text-fg-secondary text-3.5 leading-4">
          {{ $t("cards.auto-buy-usdt-attention") }}
        </div>
      </div>
    </UITransition>

    <!--    attention block -->
    <div class="bg-bg-red-light p-4 rounded flex-col">
      <div class="flex text-4.5 font-medium leading-6 text-fg-primary mb-3">
        {{ $t("createCard.payments.attention") }}
      </div>

      <div class="flex">
        <ul class="flex flex-col gap-2 list-disc pl-5 text-4 text-fg-primary">
          <li class="leading-5">
            {{ $t("createCard.payments.attention.anotherNetwork") }}
          </li>
          <li
            v-if="
              isNil(userStore.userFees.deposit_fee_usdt) ||
              Number(userStore.userFees.deposit_fee_usdt) > 0
            "
            class="leading-5">
            {{ $t("createCard.payments.network.depositServiceFee") }}
          </li>
          <li
            v-if="accountOptionValue === 'BTC'"
            class="leading-5">
            {{ $t("attention-deposit-with-btc-auto-buy") }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
