<script lang="ts" setup>
// components
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UiSelect from "@/components/ui/Select/SelectPromoCard.vue";
import Warning from "@/components/ui/Warning/Warning.vue";
import DepositWarning from "@/components/ui/Warning/DepositWarning.vue";
import AmountBalance from "@/components/Teams/AmountBalance.vue";
import AutoRefill from "@/components/Transaction/New/Actions/AutoRefill.vue";
import UiCheckbox from "@/components/ui/Checkbox/SquareCheckbox.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import Information from "@/components/ui/InfoMessage/Information.vue";
import InputCopy from "@/components/ui/Input/InputCopy.vue";
import AccountElement from "@/components/ui/Select/AccountElement.vue";
import { AttachPromoCode } from "@/components/Widgets/PromoCode/helper";
import PromoCodeWidget from "@/components/Widgets/PromoCode/Index.vue";

// svg cards
// @ts-ignore
import adv from "@/assets/svg/cards/adv.svg";
// @ts-ignore
import all from "@/assets/svg/cards/all.svg";
// @ts-ignore
import fb from "@/assets/svg/cards/fb-prem.svg";
// @ts-ignore
import platinum from "@/assets/svg/cards/platinum-credit.svg";
// @ts-ignore
import Black from "@/assets/svg/cards/pst-black-view.svg";
// @ts-ignore
import Exclusive from "@/assets/svg/cards/exclusive-view.svg";
// @ts-ignore
import c3ds from "@/assets/svg/cards/3ds.svg";
// @ts-ignore
import visa from "@/assets/svg/cards/visa.svg";
// @ts-ignore
import mastercard from "@/assets/svg/cards/mastercard.svg";

import QrCode from "qrcode";
import { TrackerEvent, useTracker } from "@/composable";
import {
  computed,
  ref,
  onMounted,
  onBeforeUnmount,
  watch,
  reactive,
} from "vue";
import { useRouter } from "vue-router";
import { useCardsStore } from "@/stores/cards";
import { openPrivacy, openRestrictions, openTos } from "@/helpers/other";
import { useAxios } from "@/helpers/axios";
import { useUserStore } from "@/stores/user";
import { CardService } from "@/modules/services/card";
import type { Ref } from "vue";
import { useI18n } from "vue-i18n";
import InputNumber from "@/components/ui/Input/InputNumber.vue";
import { UserService } from "@modules/services/user";
import type { TExchangeRates } from "@/types/dictionary/dictionary.types";
import { useQueryClient } from "vue-query";
import { userAccountsKey } from "@/config/queryConfig";
import DepositTotals from "@/components/Cards/CreateCardWithAutoBuy/Stages/Deposite/Totals/Index.vue";
import type { IPromoCodeModel } from "@modules/services/promocode";
import SubscriptionsUpLimitSingle from "@/components/Widgets/SubscriptionsUpLimitSingle/SubscriptionsUpLimitSingle.vue";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useUserAccountsMod } from "@/components/Cards/CreateCardOldDesign/useUserAccountsMod";

// systems
export interface Props {
  userTariff: Array<any>;
  card?: any;
  cardsInfo: any;
  activeRequest?: any;
}

const { subscriptionsStatus } = useSubscriptionsInfo();
const tracker = useTracker();

const queryClient = useQueryClient();
const props = defineProps<Props>();
const emit = defineEmits([
  "card-issue-autobuy",
  "hide",
  "change-autobuy-close",
]);
const router = useRouter();
const cardsStore = useCardsStore("cards-accounts");
const { accounts, activeAccount, usdAccount, usdtAccount } = useUserAccountsMod(
  {
    immediate: true,
    selectUsdtAccount: true,
  }
);
const userStore = useUserStore();
const { isTeamOwner, isTeamMember, userActualLimit } = userStore;
const userIsWarn = computed<boolean>(() => !!userStore?.user?.show_warn);

// data
const { t } = useI18n();
const copied = ref<string>("");
const step = ref<number>(1);
const errorToIssue = ref<string>("");
const loading = ref<boolean>(false);
const qrSrc = ref<any>("");
const rates = ref<TExchangeRates | null>(null);
const timer = ref<any>(null);
const secondTimer = ref<any>(null);
const lastTransactionTs = ref<any>(null);
const transactionReceived = ref<boolean>(false);
const additionalFeeUsd = ref<number>(0);
const agreement = ref<any>({
  checked: false,
  valid: true,
});
const autoRefillForm = ref<any>({
  minimumBalance: "15",
  amountRefill: "20",
  isActive: true,
});
const resultCard = ref<any>({
  description: "",
  id: 0,
  mask: "0",
});

const depositTotals = ref<InstanceType<typeof DepositTotals> | null>(null);

const cardForIssue = ref<any>({
  bin: "",
  system: 0,
  type: "",
  start_balance: "50",
  description: "",
  account: "",
  address: "",
  id: "",
  count: 1,
  code: "",
});

// methods
const setCopied = (value: string) => (copied.value = value);
const waitUntil = async (condition: Ref<any>) => {
  await setTimeoutCustom(300);

  if (!condition.value) {
    await waitUntil(condition);
  }
};
const setTimeoutCustom = (delay: number = 300) => {
  return new Promise((resolve: any) => {
    setTimeout(resolve, delay);
  });
};
const setCardSystem = (system: string) => {
  cardForIssue.value.system = system;
  cardForIssue.value.bin = "";
};
const setCardBalance = (value: number) => {
  cardForIssue.value.start_balance = String(value);
  errorToIssue.value = "";
};
const onCheckboxChange = (value: boolean) => {
  agreement.value.checked = value;
  agreement.value.valid = true;
};
const setBin = (bin: any) => (cardForIssue.value.bin = bin);

const setAccount = (account: any) => (cardForIssue.value.account = account);

// const exchangeAmount = (amount: number | string) => {
//   const moneyList = rates.value;
//   const isoCode = cardForIssue.value.account?._currency?.iso_code
//     ? String(cardForIssue.value.account?._currency?.iso_code)
//     : "USDT";
//
//   if (!moneyList) return 1;
//
//   // @ts-ignore
//   const exchangeRate = moneyList[isoCode].USD || 1;
//
//   return new BigNumber(amount).dividedBy(exchangeRate.toString()).toNumber();
// };
const createRefill = async (id: number) => {
  const response = await CardService.createRefill({
    card_id: id,
    minimum_balance: autoRefillForm.value.minimumBalance,
    amount_refill: autoRefillForm.value.amountRefill,
  });

  if (!response.status) {
    console.error("CreateCard/Form->createRefill error handled: ", response);
    return;
  }
};

const availableBalance = computed(() => {
  const currentCurrency = cardForIssue.value.account?._currency?.iso_code;
  if (!rates.value || !currentCurrency) {
    return 0;
  }

  const code: "USDT" | "BTC" | "EUR" = currentCurrency;

  const rate = currentCurrency === "USD" ? "1" : rates.value[code]["USD"];
  const fullAvailableUsdSum =
    ((cardForIssue.value.account?.balance * parseFloat(rate) -
      selectedTariff.value?.card_price) *
      (1 - selectedTariff.value?.fee_topup / 100)) /
    cardForIssue.value.count;
  return fullAvailableUsdSum > 1
    ? parseFloat(fullAvailableUsdSum.toFixed(2))
    : 0;
});

const clearAllIntervals = () => {
  clearInterval(timer.value);
  clearInterval(secondTimer.value);
};
const startGlobalWatch = () => {
  startWatchForTransaction();
  startWatch();
};
const startWatchForTransaction = () => {
  clearInterval(secondTimer.value);

  secondTimer.value = setInterval(async () => {
    const response = await CardService.checkAutoBuyTransaction({
      urlParams: {
        id: cardForIssue.value.address,
      },
    });

    if (!response.status) {
      return;
    }
    const ts = response.data;
    if (lastTransactionTs.value === null) {
      lastTransactionTs.value = ts;
    }
    if (lastTransactionTs.value !== ts) {
      transactionReceived.value = true;
      emit("change-autobuy-close");
    }
  }, 1000);
};
const startWatch = () => {
  clearInterval(timer.value);

  timer.value = setInterval(() => {
    if (cardForIssue.value?.id) {
      useAxios()
        .get("/auto-buy/detail/" + cardForIssue.value.id)
        .then((res: any) => {
          const result = res.data.data.status;

          if (result === 20) {
            router.push(isTeamOwner ? "/team" : "/app/");
          }

          if (result === 30) {
            router.push("/app/promo-cards");
          }
        });
    }
  }, 5000);
};

const createCard = async () => {
  loading.value = true;

  // validation
  if (!cardForIssue.value.system) {
    cardForIssue.value.system = availableSystems.value[0];
  }
  if (cardForIssue.value.system === "mastercard") {
    cardForIssue.value.system = "5";
  }
  if (cardForIssue.value.system === "visa") {
    cardForIssue.value.system = "4";
  }
  if (!cardForIssue.value.bin) {
    cardForIssue.value.bin = availableBins.value[0];
  }
  if (Number(cardForIssue.value.start_balance) < 1) {
    errorToIssue.value = "Desired balance have to be at least 1$";
    loading.value = false;
    return;
  }
  if (
    Number(cardForIssue.value.count) < 1 ||
    Number(cardForIssue.value.count) > cardMaxCount.value
  ) {
    errorToIssue.value = t("cards.create.countError");
    loading.value = false;
    return;
  }
  if (agreement.value.checked === false) {
    agreement.value.valid = false;
    loading.value = false;
    return;
  }

  // set card type. adv, all, 3ds...
  cardForIssue.value.type = props.card.code;

  // prepayment section
  if (prepaymentAvailable.value) {
    cardForIssue.value.address =
      usdtAccount.value?.addresses[0]?.address || "Something went wrong";

    cardForIssue.value.account_id = usdtAccount.value?.id || "";

    qrSrc.value = await QrCode.toDataURL(cardForIssue.value.address);
    useAxios()
      .post("/auto-buy/store", {
        bin: cardForIssue.value.bin,
        system: cardForIssue.value.system,
        type: cardForIssue.value.type,
        start_balance: depositTotals?.value?.totalValue ?? "50",
        id: cardForIssue.value.id,
        code: cardForIssue.value.code,
      })
      .then((response) => {
        if (cardForIssue.value.code) {
          tracker.logEvent(TrackerEvent.PROMOCODE_ACTIVATE, {
            code: cardForIssue.value.code,
          });

          tracker.setUserProperty(
            TrackerEvent.PROMOCODE_USED,
            cardForIssue.value.code
          );
        }
        step.value = 2;
        cardForIssue.value.id = response.data.data.id;
        cardForIssue.value.send =
          Number(prepaymentSend.value) + additionalFeeUsd.value;

        emit("card-issue-autobuy", response.data.data.id);
        startGlobalWatch();
      })
      .catch((ex) => {
        const error =
          ex.response?.data?.errors?.start_balance?.join("") ||
          ex.response?.data?.message ||
          "Something went wrong!";
        errorToIssue.value = error;
        console.error("CreateCard/Form->createCard error handled: ", ex);
      })
      .finally(() => {
        loading.value = false;
      });

    return;
  }

  cardForIssue.value.account_id = cardForIssue.value.account.id;

  // if cards count > 1
  if (Number(cardForIssue.value.count) > 1) {
    const isAvailable = await multiBuyCheck();

    if (isAvailable) {
      await multiBuyRequest();
    }
  } else {
    try {
      if (promoCodeValue.value) {
        const attachPromoCode = new AttachPromoCode(promoCodeValue.value);
        const resAttach = await attachPromoCode.attach();
        if (!resAttach.status) {
          serverError.status = true;
          return;
        }
      }
      const res = await useAxios().post("/card/buy", {
        ...cardForIssue.value,
        account_id: cardForIssue.value.account.id,
      });

      resultCard.value = res.data.data;

      // Create auto refill
      if (resultCard.value.id && autoRefillForm.value.isActive) {
        await createRefill(resultCard.value.id);
      }

      step.value = 3;

      tracker.logEvent(TrackerEvent.CARD_ISSUED, {
        slug: cardForIssue.value.type,
      });

      emit("hide");
    } catch (ex: any) {
      if (
        ex.response?.data?.message === "KYC limit: user already has one card"
      ) {
        errorToIssue.value = "KYC limit: user already has one card";
        return;
      }
      if (ex.response !== undefined) {
        errorToIssue.value = ex?.response?.data?.message;
        return;
      }
      errorToIssue.value = ex.toString();
    } finally {
      loading.value = false;
    }
  }

  loading.value = false;
};

const getMultiBuyBody = () => ({
  account_id: cardForIssue.value.account_id || cardForIssue.value.account.id,
  type: cardForIssue.value.type,
  start_balance: cardForIssue.value.start_balance,
  description: cardForIssue.value.description,
  system: cardForIssue.value.system,
  bin: cardForIssue.value.bin,
  count: cardForIssue.value.count,
});

const multiBuyCheck = async () => {
  const checkResult = await CardService.multiBuyCheck(getMultiBuyBody());

  if (checkResult?.status && checkResult?.data?.success) {
    return true;
  } else {
    errorToIssue.value = checkResult?.message || "";
    return false;
  }
};

// Multi Buy Request
const multiBuyRequest = async () => {
  try {
    const body = getMultiBuyBody();
    await CardService.multiBuy(body);

    tracker.logEvent(TrackerEvent.CARD_ISSUED, {
      slug: body.type,
      count: body.count,
    });

    step.value = 3;

    emit("hide");
  } catch (ex: any) {
    if (ex.response !== undefined) {
      errorToIssue.value = ex?.response?.data?.message;
      console.error("CreateCard/Form->multiBuyRequest error handled: ", ex);
      return;
    }
    errorToIssue.value = ex.toString();
  }
};

// ui computed
const cardImageTextColor = computed<string>(() => {
  const colors: any = {
    white: "text-greyscale-200",
    purple: "text-portage",
    green: "text-teal",
    blue: "text-sky",
  };

  return colors[props.card?.color || "white"];
});

// other computed
// get card by code
const cardByCode = computed(() => {
  const cards: any = {
    adv,
    all,
    "fb-prem": fb,
    "platinum-credit": platinum,
    "3ds": c3ds,
    "pst-black": Black,
    "pst-black-prem": Black,
    "pst-black-uniq-bin": Black,
    exclusive: Exclusive,
  };

  return cards[props.card.code];
});
// max count of cards error
const cardMaxCount = computed<number>(() =>
  ["fb-prem", "adv", "platinum"].includes(props.card?.code) ? 25 : 10
);
const cardsCountErrorExist = computed<boolean>(
  () =>
    !(
      cardForIssue.value.count <= cardMaxCount.value &&
      cardForIssue.value.count > 0
    )
);
// result card mask
const cardMask = computed(() =>
  resultCard.value?.mask?.charAt(0) === "4" ? visa : mastercard
);
/**
 * PST-65
 * Amount of the card cost and the commission for replenishment
 */
// const calculatedNeedAmount = computed(() => {
//   const topUp = new BigNumber(
//     Number(cardForIssue.value.start_balance)
//   ).dividedBy(
//     new BigNumber(1).minus(
//       new BigNumber(selectedTariff.value?.fee_topup).dividedBy(100)
//     )
//   );
//   return topUp.plus(selectedTariff.value?.card_price).toNumber();
// });
// prepayment send
const prepaymentSend = computed(() => {
  let USDT: any = 1;

  try {
    const rate = rates.value && rates.value["USD"];

    if (rate) {
      USDT = rate["USDT"];
    }
  } catch (ex) {
    console.error("CreateCard/Form->prepaymentSend error handled: ", ex);
  }

  return (
    (Number(
      (props.activeRequest?.start_balance || cardForIssue.value.start_balance) /
        (1 - selectedTariff.value?.fee_topup / 100)
    ) +
      Number(selectedTariff.value?.card_price)) /
    Number(USDT || 1)
  ).toFixed(4);
});
// selected tariff
const selectedTariff = computed(() =>
  props.userTariff.find((tariff: any) => tariff.slug === props.card?.code)
);
// visa & mastercard
const availableSystems = computed<Array<string>>(() => {
  const systems: any = {
    4: "visa",
    5: "mastercard",
  };

  try {
    if (
      Object.keys(props.cardsInfo)?.length === 0 ||
      !props.cardsInfo[props.card.code]
    ) {
      return [];
    }

    return props.cardsInfo[props.card.code].reduce(
      (accum: Array<string>, item: any) => {
        accum.push(systems[item.bin.toString().charAt(0)]);

        return [...new Set(accum)];
      },
      []
    );
  } catch (ex) {
    console.error("CreateCard/Form->availableSystems error handled: ", ex);
    return [];
  }
});
// available bins
const availableBins = computed<Array<string>>(() => {
  let result: any[] = [];

  if (availableSystems.value.length > 1) {
    if (
      ["visa", 4, "4"].includes(cardForIssue.value.system) ||
      (cardForIssue.value.system === 0 && availableSystems.value[0] === "visa")
    ) {
      props.cardsInfo[props.card.code].forEach((item: any) => {
        if (item.bin.toString()[0] === "4") {
          // if (cardForIssue.value.bin.toString().charAt(0) !== "4") {
          //   setBin(item.bin);
          // }
          result.push(item.bin);
        }
      });
    } else if (
      ["mastercard", 5, "5"].includes(cardForIssue.value.system) ||
      (cardForIssue.value.system === 0 &&
        availableSystems.value[0] === "mastercard")
    ) {
      props.cardsInfo[props.card.code].forEach((item: any) => {
        if (item.bin.toString()[0] === "5") {
          // if (cardForIssue.value.bin.toString().charAt(0) !== "5") {
          //   setBin(item.bin);
          // }
          result.push(item.bin);
        }
      });
    }

    return result;
  } else if (availableSystems.value.length === 1) {
    result = props.cardsInfo[props.card.code].map((item: any) => {
      return item.bin;
    });
    return result;
  }
  return [];
});

// prepayment available?
const prepaymentAvailable = computed<boolean>(() => {
  if (userIsWarn.value) return false;
  const userHasCards = cardsStore.state.total !== 0;
  const userHasMoney = accounts.value?.find((item: any) => item.balance > 0);
  return !userHasCards && !userHasMoney && !isTeamMember;
});

// watchers
watch(
  () => prepaymentSend.value,
  async (newValue: any) => {
    const amount = Number(newValue);

    if (amount < 50) {
      // set 5 usd additional fee
      additionalFeeUsd.value = 5;
      await tracker.logEvent(TrackerEvent.DEPOSIT_NOTIFICATION_DISPLAYED);
    } else {
      additionalFeeUsd.value = 0;
    }
  }
);

// setup
queryClient.invalidateQueries([userAccountsKey]);
UserService.exchangeRates().then((res: any) => (rates.value = res.data));

// on mounted hook
onBeforeUnmount(() => {
  if (timer.value || secondTimer.value) {
    clearAllIntervals();
  }
});
onMounted(async () => {
  // 1. Account for card for issue
  cardForIssue.value.account = usdAccount.value;

  // 2. Clear interval
  clearAllIntervals();

  // 3. if active request
  if (props?.activeRequest) {
    step.value = 2;

    await waitUntil(usdtAccount);

    if (usdtAccount.value?.addresses) {
      cardForIssue.value.address =
        usdtAccount.value?.addresses[0]?.address || "Something went wrong";
    }

    qrSrc.value = await QrCode.toDataURL(cardForIssue.value.address);

    cardForIssue.value.send =
      Number(prepaymentSend.value) + additionalFeeUsd.value;
    cardForIssue.value.id = props.activeRequest?.id;
    startGlobalWatch();
  }
  if (props.card.code === "fb-prem") {
    autoRefillForm.value.isActive = true;
  }
});

defineExpose({ clearAllIntervals });

const promoCodeValue = ref("");
const serverError = reactive({
  status: false,
  msg: t("cards.create.error.promocode_server_error"),
});

const promoCodeDiscount = ref(0);
function changePromoCodeHandler(v: string, d: IPromoCodeModel) {
  promoCodeValue.value = v;
  cardForIssue.value.code = v;
  if (!v) {
    promoCodeDiscount.value = 0;
    return;
  }
  promoCodeDiscount.value = Number(d.fields.card_buy_discount_percent);
}
</script>

<template>
  <div :class="$style.root">
    <!--  Step 1. Choose cards options -->
    <div
      v-show="step === 1"
      class="flex flex-col items-center gap-6">
      <!--   Card image 2   -->
      <div
        class="py-10 mx-auto"
        :class="cardImageTextColor">
        <!--    Card system    -->
        <div :class="$style.cardMockup">
          <DynamicIcon
            v-if="['visa', 4, '$'].includes(cardForIssue.system)"
            :class="$style.cardMockupSystem"
            name="visa"
            width="60"
            height="20"
            path="cards" />

          <DynamicIcon
            v-else
            :class="$style.cardMockupSystem"
            name="mastercard"
            width="60"
            height="20"
            path="cards" />

          <DynamicIcon
            :name="card?.code"
            path="cards" />
        </div>
      </div>
      <SubscriptionsUpLimitSingle v-if="subscriptionsStatus" />

      <!--   Filters grid   -->
      <div class="flex items-center justify-between gap-6 w-full">
        <!--    Card system    -->
        <UiSelect
          :value="cardForIssue.system"
          :label="$t('Card type')"
          label-type="inside"
          label-class="text-sm font-medium  py-1 pb-3"
          :default-value="availableSystems[0]"
          select-class="pb-3 pt-9 pl-3"
          :options="availableSystems"
          class="w-full"
          data-cy="card_system_select"
          @change="setCardSystem">
          <template #withIcon="slotProps">
            <div
              class="flex items-center w-full gap-3 font-extrabold text-h6 uppercase">
              <span>{{ slotProps.option }}</span>
            </div>
          </template>
        </UiSelect>

        <!--   Card bins  -->
        <UiSelect
          :default-value="cardForIssue.bin || availableBins[0]"
          :label="$t('Available BINS')"
          label-type="inside"
          label-class="text-sm font-medium  py-1 pb-3"
          select-class="pb-3 pt-9 pl-3"
          :options="availableBins"
          class="w-full"
          data-cy="bins_select"
          @change="setBin">
          <template #withIcon="slotProps">
            <div class="flex items-center w-full gap-3 font-extrabold text-h6">
              <span>{{ slotProps.option }}</span>
            </div>
          </template>
        </UiSelect>
      </div>

      <!--    Accounts select    -->
      <UiSelect
        v-if="!prepaymentAvailable"
        :value="cardForIssue.account"
        label="accounts"
        :default-value="activeAccount || 'loading'"
        label-type="inside"
        label-class="text-sm font-medium  py-1 pb-3"
        select-class="pb-3 pt-9 pl-3"
        :options="accounts"
        class="w-full"
        data-cy="account_select"
        @change="setAccount">
        <template #withIcon="slotProps">
          <AccountElement
            :rates="rates"
            :account="slotProps.option" />
        </template>
      </UiSelect>
      <!--   Select balance     -->
      <AmountBalance
        :value="cardForIssue.start_balance"
        :label="$t('Desired card balance in $')"
        :available="availableBalance"
        class="w-full"
        @change="setCardBalance" />

      <!--      Select count-->
      <InputNumber
        v-if="!prepaymentAvailable"
        :value="cardForIssue.count"
        :copy="false"
        :label="$t('cards.create.countLabel')"
        min="1"
        :max="cardMaxCount"
        input-class="font-extrabold text-h6 !h-[70px]"
        py-2
        :error="
          cardsCountErrorExist
            ? t('cards.create.countError2', { f: cardMaxCount })
            : undefined
        "
        label-type="inside"
        class="w-full"
        data-cy="count_input"
        @input="(v) => (cardForIssue.count = v)" />

      <!--    Set description    -->
      <InputCopy
        :value="cardForIssue.description"
        :label="$t('comment')"
        class="w-full"
        input-class="font-extrabold text-h6 !h-[70px]"
        data-cy="description_input"
        @input="(v) => (cardForIssue.description = v)" />

      <!--    Card auto refill    -->
      <div
        v-if="
          !['all', '3ds'].includes(card.code) &&
          Number(cardForIssue.count) === 1
        "
        class="flex flex-col">
        <div class="flex justify-between items-center pb-4 leading-none">
          <span class="text-h6 font-extrabold">
            {{ $t("deposit.autoRefill.title") }}
          </span>

          <DynamicIcon
            v-tooltip="{
              content: $t('deposit.autoRefill.description'),
            }"
            name="alert-circle"
            class="w-5 h-5 cursor-pointer hover:text-secondary-base transition-all" />
        </div>

        <AutoRefill
          :minimum-balance="autoRefillForm.minimumBalance"
          :amount-refill="autoRefillForm.amountRefill"
          :is-active="autoRefillForm.isActive"
          @set-min="(v) => (autoRefillForm.minimumBalance = v)"
          @set-refill="(v) => (autoRefillForm.amountRefill = v)"
          @set-active="(v) => (autoRefillForm.isActive = v)" />
      </div>

      <!-- PromoCode -->
      <!--      <div-->
      <!--        v-if="!prepaymentAvailable && cardForIssue.count === 1"-->
      <!--        class="w-full"-->
      <!--      >-->
      <!--        <PromoCodeWidget @change-promo-code="changePromoCodeHandler" />-->
      <!--      </div>-->
      <PromoCodeWidget @change-promo-code="changePromoCodeHandler" />

      <!-- Agreements -->
      <div
        class="font-medium text-greyscale-600 text-base flex items-start gap-6">
        <UiCheckbox
          label=""
          :checked="agreement.checked"
          data-cy="agreement"
          :custom-class="!agreement.valid ? $style.agreement : undefined"
          @change="onCheckboxChange">
          <template #label>
            <div
              class="text-greyscale-600 translate-y-[1px]"
              :class="{ 'text-error-base': !agreement.valid }">
              {{ $t("cards.create.agreement_1") }}
              <a
                class="text-greyscale-900 cursor-pointer"
                :class="{ 'text-error-base': !agreement.valid }"
                data-cy="privacy_policy"
                @click="openPrivacy">
                {{ $t("cards.create.agreement_2") }} </a
              >,
              <a
                class="text-greyscale-900 cursor-pointer"
                :class="{ 'text-error-base': !agreement.valid }"
                data-cy="terms_of_use"
                @click="openTos">
                {{ $t("cards.create.agreement_4") }}
              </a>
              {{ $t("cards.create.agreement_3") }}
              <a
                class="text-greyscale-900 cursor-pointer"
                :class="{ 'text-error-base': !agreement.valid }"
                data-cy="terms_of_use"
                @click="openRestrictions">
                {{ $t("cards.create.agreement_5") }} </a
              >.
            </div>
          </template>
        </UiCheckbox>
      </div>

      <!--   Error    -->
      <span
        v-if="errorToIssue && errorToIssue !== 'KYC limit'"
        data-cy="errorToIssue"
        class="col-span-2 text-sm text-error-light">
        {{ errorToIssue }}
      </span>
      <Warning
        background="yellow"
        class="col-span-2">
        <template #title>
          {{ $t("label.attention") }}
        </template>
        <template #text>
          {{
            t("warning.notCompatibleWithWallets", {
              w: "Apple Pay, Google Pay",
            })
          }}
        </template>
      </Warning>
      <Warning
        v-if="errorToIssue && errorToIssue === 'KYC limit' && !!userActualLimit"
        data-cy="errorToIssue"
        :error="errorToIssue"
        icon="danger"
        :data="{
          kyc_actual: userActualLimit.userLimit.slug,
          kyc_deposit_limit: userActualLimit.userLimit.card_deposit_limit,
        }"
        class="col-span-2" />

      <!--   Create card button   -->
      <UiButton
        :title="$t('Order')"
        class="w-full"
        :disabled="loading || cardsCountErrorExist"
        data-cy="order_button"
        @click="createCard" />

      <Information
        v-if="Number(prepaymentSend) < 50 && prepaymentAvailable"
        class="mb-10">
        {{ $t("deposit.cryptocurrency.withFee") }}
      </Information>
      <p
        v-if="serverError.status"
        class="text-red-500 text-center font-normal">
        {{ serverError.msg }}
      </p>
    </div>

    <!--  Step 2  -->
    <div
      v-show="step === 2"
      class="flex flex-col items-center gap-6">
      <template v-if="!transactionReceived">
        <div
          v-if="cardForIssue.value?.address !== ''"
          class="pb-2 mx-auto">
          <div
            v-if="qrSrc !== ''"
            :style="`background-image: url('${qrSrc}')`"
            class="bg-no-repeat bg-contain bg-center w-full min-h-[200px] min-w-[200px]" />
        </div>

        <DepositWarning />

        <InputCopy
          :value="cardForIssue?.address"
          :label="$t('USDT TRC20 Wallet Address')"
          input-class="font-extrabold text-sm h-[58px]"
          disabled
          :copied-value="copied"
          :copy="true"
          class="w-full"
          data-cy="issue_card_wallet_addr"
          @input="(v) => cardForIssue?.address"
          @copied="setCopied" />
        <!--        <InputCopy-->
        <!--          :value="cardForIssue?.send"-->
        <!--          @input="(v) => cardForIssue?.send"-->
        <!--          :label="$t('Amount_to_send in_USDT')"-->
        <!--          input-class="font-extrabold text-h6"-->
        <!--          class="w-full"-->
        <!--          disabled-->
        <!--          data-cy="issue_card_send_sum"-->
        <!--        />-->
        <DepositTotals
          v-if="cardForIssue"
          ref="depositTotals"
          :balance="cardForIssue.start_balance"
          :fake-tariff="selectedTariff"
          :user-tariff="selectedTariff"
          :fee="additionalFeeUsd > 0 ? additionalFeeUsd : undefined"
          :percent-discount-cost-card-promo-code="promoCodeDiscount"
          add-rate="USDT"
          class="w-full" />
      </template>

      <template v-else>
        <div class="flex flex-col items-center gap-6">
          <DynamicIcon
            name="check"
            class="w-[120px] h-[120px]" />

          <h3 class="text-gray-700 mb-10">
            {{ $t("createCardForm.paymentFound") }}
          </h3>
        </div>
      </template>

      <div class="flex items-center justify-between w-full pb-4">
        <span class="font-medium text-greyscale-600 text-base">
          {{ $t("Status") }}
        </span>

        <span
          class="font-extrabold text-base"
          data-cy="issue_card_status"
          :class="
            transactionReceived ? 'text-success-dark' : 'text-warning-dark'
          ">
          {{
            $t(
              transactionReceived
                ? "createCardForm.processing"
                : "Pending Payment"
            )
          }}
        </span>
      </div>
    </div>

    <!--  Step 3  -->
    <div
      v-show="step === 3"
      class="flex flex-col items-center gap-6">
      <div class="text-center flex flex-col gap-2 pb-4">
        <h4>
          {{
            cardForIssue.count > 1
              ? $t("cards.create.multiSuccessTitle")
              : $t("Card is issued")
          }}
        </h4>

        <p class="text-greyscale-600 font-medium text-base w-10/12 mx-auto">
          {{
            cardForIssue.count > 1
              ? $t("cards.create.multiSuccessDesc")
              : $t("cards.create.issuedMessage")
          }}
        </p>
      </div>

      <div
        class="flex items-center gap-3 bg-greyscale-100 w-full mb-4 rounded-[12px]">
        <div :class="cardImageTextColor">
          <div :class="$style.WidgetCardIssueForm__card_issued__mockup">
            <component
              :is="cardMask"
              v-if="resultCard.value?.mask"
              :class="$style.WidgetCardIssueForm__card_issued__mockup_system"
              width="60"
              height="20" />

            <component :is="cardByCode" />
          </div>
        </div>

        <div class="flex flex-col gap-2 p-3">
          <div
            class="font-extrabold"
            :data-cy="card.title">
            {{ card.title }}
          </div>
          <div
            v-if="cardForIssue.count > 1"
            class="flex flex-col text-base">
            <span class="font-medium text-greyscale-600">
              {{ $t("cards.create.countLabel") }}
            </span>

            <span
              class="font-extrabold break-all"
              data-cy="description_from_last_step">
              {{ cardForIssue.count }}
            </span>
          </div>
          <div class="flex flex-col text-base">
            <span class="font-medium text-greyscale-600">
              {{ $t("comment") }}
            </span>

            <span
              class="font-extrabold break-all"
              data-cy="description_from_last_step">
              {{
                cardForIssue.count > 1
                  ? cardForIssue.description
                  : resultCard.description
              }}
            </span>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-6 w-full">
        <UiButton
          type="secondary"
          data-cy="dashboard_section_button"
          :title="$t('Go to dashboard')"
          class="w-full"
          @click="$router.push({ path: isTeamOwner ? '/team' : '/app' })" />

        <UiButton
          :title="$t('Go to cards')"
          data-cy="cards_section_button"
          class="w-full"
          @click="
            $router.push({ path: `${isTeamOwner ? '/team' : '/app'}/cards` })
          " />
      </div>
    </div>
  </div>
</template>

<style lang="scss" module>
.root {
  &__card_issued__mockup {
    position: relative;
  }

  &__card_issued__mockup_system {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 2;
  }
}

.icon {
  @apply bg-white border w-max rounded-sm p-[7px] mb-1 min-w-[34px];
  & svg {
    @apply w-[18px] h-[18px];
  }
}

.cardMockup {
  @apply relative;
}

.cardMockupSystem {
  @apply absolute bottom-5 right-5;
}

.agreement {
  @apply border-secondary-base;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 96, 96, 0.9);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(231, 96, 96, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(231, 96, 96, 0);
  }
}
</style>
