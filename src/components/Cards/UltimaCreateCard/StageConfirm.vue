<script lang="ts">
export default {
  name: "UltimaCreateCardConfirmStage",
};
</script>
<script lang="ts" setup>
import { computed, ref, onMounted, toRef } from "vue";
import { useUserAccounts } from "@/composable/useUserAccounts";
import { useI18n } from "vue-i18n";
import { UserService } from "@modules/services/user";
import type { TTransferState } from "@/components/Transaction/Latest/types";
import { useUserExchangeRates } from "@/composable/User/ExchangeRates";
import { ExceptionCause, ExceptionReason } from "@/config/Exception";
import { loggerConsole } from "@/helpers/logger/Logger";
import SelectAccount from "@/components/ui/Select/SelectAccount.deprecated.vue";
import { useUserCardBuyPost } from "@/composable/API/useUserCardBuyPost";
import { TrackerEvent, useTracker, getCurrencySymbolByIso } from "@/composable";
import { AttachPromoCode } from "@/components/Widgets/PromoCode/helper";
import CardIssueSummary from "@/components/CardIssueSummary/CardIssueSummary.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import type { TCardTariff } from "@/types/user/user.types";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { useCardAutoRefillPost } from "@/composable/API/useCardAutoRefillPost";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { prepareAccountBalance } from "@/helpers";
import { calcTopUpFee } from "@/helpers/calcTopUpFee";

type Props = {
  stateForm: any;
  slug: string;
};

const props = defineProps<Props>();

const tracker = useTracker();

const cardForIssueState: any = toRef<any>(() => props.stateForm?.cardForIssue);
const cardForIssue = ref<any>({
  system: cardForIssueState.value?.system ?? 0,
  type: cardForIssueState.value?.type ?? "",
  start_balance: cardForIssueState.value?.start_balance ?? "50",
  minValue: cardForIssueState.value?.minValue ?? "50",
  description: cardForIssueState.value?.description ?? "",
  account: cardForIssueState.value?.account ?? "",
  address: cardForIssueState.value?.address ?? "",
  id: cardForIssueState.value?.id ?? "",
  count: cardForIssueState.value?.count ?? 1,
});

const promoCodeDataState = toRef<any>(() => props.stateForm?.promoCodeData);
const promoCodeData = ref<any>({
  data: promoCodeDataState.value?.data ?? undefined,
  list: promoCodeDataState.value?.list ?? [],
  code: promoCodeDataState.value?.code ?? "",
});

const percentDiscountCostCardPromoCode = computed(() => {
  if (!promoCodeData.value.code) return 0;
  const data = promoCodeData?.value.data;
  if (data?.fields) {
    return Number(data.fields?.card_buy_discount_percent || 0);
  } else {
    return 0;
  }
});

const accountIdState: any = toRef<any>(() => props.stateForm?.accountId);
const tState = ref<TTransferState>({
  type: props.stateForm?.type ?? "",
  step: props.stateForm?.step ?? 1,
  toId: -1,
  toDirection: "",
  toAmount: "0",
  fromId: accountIdState.value ?? -1,
  fromDirection: "account",
  fromAmount: "0",
  isSubmit: false,
  isConfirm: false,
  error: {},
});

const resultCard = ref<any>({
  description: "",
  id: 0,
  mask: "0",
});

const isLoading = ref<boolean>(false);

const emit = defineEmits([
  "confirmHandler",
  "confirmErrorHandler",
  "kyc-card-limit",
  "kyc-deposit-limit",
]);

const { accounts } = useUserAccounts();

const userTariff = ref<Array<any>>([]);
const userSpecial = ref<any>(null);
const logger = loggerConsole();

// data
const { t } = useI18n();

const { data: rates } = useUserExchangeRates();

const loadUserTariff = async () => {
  const response = await UserService.tariff();
  const special = await UserService.special();

  if (!response.status) {
    useCallToast({
      title: t("toast.error"),
      body: response.message || t("toast.error"),
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });

    logger.error(ExceptionCause.USER_TARIFF, ExceptionReason.FAILED_LOAD);
  }

  userSpecial.value = special?.data;
  userTariff.value = response?.data || [];
};

const isSecure = ref<boolean>(props.slug.includes("-3ds"));

const defaultSelectedTariff = computed(() => {
  const slug = isSecure.value ? props.slug.replace("-3ds", "") : props.slug;
  return userTariff.value.find((tariff: TCardTariff) => tariff.slug === slug);
});

const selectedTariff = computed(() =>
  userTariff.value.find((tariff: TCardTariff) => tariff.slug === props.slug)
);

//CardIssueSummary data
const totalPaymentUsd = computed<number | undefined>(() => {
  const startBalance = Number(cardForIssue.value?.start_balance ?? "0");
  const feeTopUpPercent = Number(selectedTariff.value?.fee_topup ?? "0");
  const feeTopUp = calcTopUpFee(startBalance, feeTopUpPercent, 1);
  return getCardPrice() + startBalance + feeTopUp;
});

const getCardPrice = (): number => {
  let cardPrice = Number(selectedTariff.value?.card_price) || 0;
  if (!cardPrice) return 0;
  return calcDiscount(cardPrice, percentDiscountCostCardPromoCode.value);
};

const calcDiscount = (price: number, discountPercent: number) => {
  return price - (price * discountPercent) / 100;
};

const ultimaTariffName = computed(() => {
  const names: { [key: string]: string } = {
    "ultima-weekly": t("cards.plan-weekly"),
    "ultima-annually": t("cards.plan-annually"),
    ultima: t("cards.plan-monthly"),
  };
  const tariffSlug =
    defaultSelectedTariff.value?.slug.replace("-3ds", "") || "";
  const plan = t("cards.plan");
  return `${plan} ${names[tariffSlug]}`;
});

const tariffMonthlyPayment = computed(() => {
  return parseFloat(defaultSelectedTariff.value?.card_price || "0").toFixed(2);
});

const tariffMonthlyPaymentWithDiscount = computed(() => {
  if (!percentDiscountCostCardPromoCode.value) return;
  const price = getCardPrice();
  return parseFloat(String(price)).toFixed(2);
});

const cardStartBalance = computed(() => {
  return parseFloat(cardForIssue.value?.start_balance).toFixed(2) || "0.00";
});

const selectedTariffFeeTopUp = computed(() => {
  const feeTopUpPercent = Number(selectedTariff.value?.fee_topup ?? "0");
  return feeTopUpPercent > 0 ? feeTopUpPercent.toFixed(1) : null;
});

const cardAccountCurrencyIsoCode = computed(
  () => cardForIssue.value.account?._currency?.iso_code
);

const cardTotal = computed(() => {
  let total = totalPaymentUsd.value;
  const isoCode = cardAccountCurrencyIsoCode.value;
  const actualRates = rates.value;

  if (!total || !isoCode || !actualRates) return "";
  if (isoCode !== "USD") {
    total = total / actualRates[isoCode]["USD"];
  }
  return `${prepareAccountBalance(total, isoCode)} ${getCurrencySymbolByIso(
    isoCode
  )}`;
});

const securityAdditionalCost = computed(() => {
  if (!isSecure.value) return;
  return (
    parseFloat(selectedTariff.value?.card_price || "0") -
    parseFloat(defaultSelectedTariff.value?.card_price || "0")
  ).toFixed(2);
});

const exchangeRates = computed(() => {
  const isoCode = cardAccountCurrencyIsoCode.value;
  if (!isoCode) return 1;
  if (isoCode === IsoCodeNames.USD) return 1;
  if (!rates.value || !rates.value[isoCode]) return 1;
  return rates.value[isoCode][IsoCodeNames.USD];
});

const exchangeRate = computed<string | undefined>(() => {
  if (exchangeRates.value === undefined) return;
  return parseFloat(exchangeRates.value).toFixed(2) || "0";
});

onMounted(async () => {
  await loadUserTariff();
});

const issueCard = async () => {
  try {
    isLoading.value = true;

    cardForIssue.value.account_id =
      cardForIssue.value.account.id ?? accountIdState.value;

    if (promoCodeData.value.code) {
      const attachPromoCode = new AttachPromoCode(promoCodeData.value.code);
      const resAttach = await attachPromoCode.attach();
      if (!resAttach.status) {
        emit("confirmErrorHandler", {
          error: t("cards.create.error.promocode_server_error"),
        });
        return;
      }
    }

    const { data } = await useUserCardBuyPost({
      account_id: cardForIssue.value.account.id ?? accountIdState.value,
      type: cardForIssue.value.type,
      start_balance: String(cardForIssue.value.start_balance),
      description: cardForIssue.value.description,
      system: cardForIssue.value.system,
      with_error_data: true,
    });

    if (!data.value?.data) {
      const error = data.value as {
        type?: string;
        field?: string;
      };

      //Kyc limit
      if (error?.type === "KYC_LIMIT") {
        emit("confirmErrorHandler", {
          error: t("cards.create.error.kyc_limit_user_has_one_card"),
        });
        emit("kyc-card-limit");
      } else if (
        error?.field &&
        ["CARD_DEPOSIT_LIMIT", "card_deposit_limit"].includes(error.field)
      ) {
        //kyc-card-limit
        emit("kyc-deposit-limit");
      } else if (error?.type) {
        //error.type
        emit("confirmErrorHandler", {
          error: t(`errors.card.create.${error.type}`),
        });
      } else {
        //default
        emit("confirmErrorHandler", {
          error: t(`errors.universal-request-error`),
        });
      }
      isLoading.value = false;
      return;
    }

    resultCard.value = data.value.data;

    const { data: autoRefillData } = await useCardAutoRefillPost({
      card_id: resultCard.value?.id,
      minimum_balance: "50",
      amount_refill: "50",
    });

    resultCard.value.auto_refill = {
      id: autoRefillData.value?.data?.id,
      active: true,
      minimum_balance: "50",
      amount_refill: "50",
    };

    emit("confirmHandler", {
      startBalance: cardForIssue.value.start_balance,
      selectedTariff: selectedTariff.value,
      countCards: cardForIssue.value.count,
      percentDiscountCostCardPromoCode: percentDiscountCostCardPromoCode.value,
      addRate: cardForIssue.value.account?._currency?.iso_code,
      cardDetail: resultCard.value,
    });

    tracker.logEvent(TrackerEvent.CARD_ISSUED, {
      slug: cardForIssue.value.type,
    });
  } catch (e) {
    emit("confirmErrorHandler", { error: t("cards.create.error.common") });
  }
};
</script>
<template>
  <div class="root">
    <!-- Confirm transfer -->
    <div class="flex flex-none items-start w-full">
      <span class="step2__title">{{
        $t("cards.card-purchasing-confirm-title")
      }}</span>
    </div>

    <div class="flex flex-none items-start w-full mt-10">
      <span class="step2__sub-title">
        {{ $t("cards.section-payment-method-title") }}</span
      >
    </div>

    <div class="flex flex-none flex-col w-full mt-3">
      <SelectAccount
        :with-icon="false"
        :disabled="true"
        :hide-chevron="true"
        :value="tState.fromId"
        :options="accounts"
        :rates="rates"
        backdrop
        searchable
        :error="tState.error.selectFrom" />
    </div>

    <!-- Total sum -->
    <div class="flex flex-none mt-4">
      <!-- Total -->
      <CardIssueSummary
        :total="cardTotal"
        :tariff-fee-top-up="selectedTariffFeeTopUp"
        :tariff-name="ultimaTariffName"
        :tariff-monthly-payment="tariffMonthlyPayment"
        :tariff-monthly-payment-with-discount="tariffMonthlyPaymentWithDiscount"
        :starting-balance-on-card="cardStartBalance"
        :account-iso-code="cardAccountCurrencyIsoCode"
        :security-additional-cost="securityAdditionalCost"
        :is-loading="isLoading"
        :exchange-rate="exchangeRate" />
    </div>

    <!-- Send button -->
    <UIButton
      size="m"
      color="black"
      class="w-full mt-10"
      data-cy="order_button"
      :disabled="isLoading"
      @click.prevent="issueCard">
      {{ $t("issue_a_card") }}
    </UIButton>
  </div>
</template>
<style lang="scss" scoped>
.root {
  @apply max-w-[27.5rem] mx-auto px-3;
}

.step2 {
  &__title {
    @apply text-5 leading-6 font-medium text-fg-primary;
  }

  &__sub-title {
    @apply text-4 leading-5 font-medium text-fg-primary;
  }
}
</style>
