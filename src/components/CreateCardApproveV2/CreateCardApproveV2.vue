<script lang="ts" setup>
import { computed, ref, toRef } from "vue";
import { useI18n } from "vue-i18n";
import { usePromoCodeAttachPost } from "@/composable/API/usePromoCodeAttachPost";
import { useUserCardMultiBuyCheckPost } from "@/composable/API/useUserCardMultiBuyCheckPost";
import { useUserCardMultiBuyPost } from "@/composable/API/useUserCardMultiBuyPost";
import {
  useUserCardBuyPost,
  type UseUserCardBuyPostReq,
} from "@/composable/API/useUserCardBuyPost";
import { useUserExchangeRatesGet } from "@/composable/API/useUserExchangeRatesGet";
import { useCreateCardSummary } from "@/components/CreateCardV2/useCreateCardSummary";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import {
  TrackerEvent,
  useAccountGet,
  useSubscriptionPlusCardAutobuy1Experiment,
  useSubscriptionsInfo,
  useTracker,
} from "@/composable";
import { IsoCodeNames } from "@/constants/iso_code_names";
import type { TCardResource } from "@/types/api/TCardResource";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import { useBinsFromConfig } from "@/composable/useBinsFromConfig";
import { useKycLimitDialog } from "@/composable/useKycLimitDialog";
import { isNil } from "lodash";
import UITransition from "@/components/ui/UITransition.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import UI2Title from "@/components/ui2/UI2Title/UI2Title.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import AccountsSelectV2 from "@/components/AccountsSelectV2/AccountsSelectV2.vue";
import CreateCardSummaryV2 from "@/components/CreateCardSummaryV2/CreateCardSummaryV2.vue";

const props = defineProps<{
  cardForIssue: TCardForIssue;
  promoCodeData: TPromoCodeResource | null;
}>();

const emit = defineEmits<{
  singleBuySuccess: [resultCard: TCardResource];
  multiBuySuccess: [];
}>();

const { t } = useI18n();
const { subscriptionsStatus } = useSubscriptionsInfo();
const { data: accountsData, isFetching: isFetchingAccounts } = useAccountGet();
const { data: ratesData, isFetching: isFetchingExchangeRates } =
  useUserExchangeRatesGet();
const tracker = useTracker();

const { getValue } = useSubscriptionPlusCardAutobuy1Experiment();
const { isActive: isAdvWithSubActive } = getValue();

// @todo start bug PST-T-4149
const { bins: binsFromConfig } = useBinsFromConfig(props.cardForIssue.type);
const binConfig = computed(
  () =>
    binsFromConfig.value.find((bin) => bin.bin === props.cardForIssue.bin) ||
    null
);
const cardBuyBodyType = computed(() => {
  if (props.cardForIssue.type === "ultima" && binConfig.value?.secure) {
    return `${props.cardForIssue.type}-3ds`;
  }
  return props.cardForIssue.type;
});
// end bug PST-T-4149

const isLoading = ref<boolean>(false);
const errorToIssue = ref<string>("");

const accounts = computed(() => {
  return accountsData.value?.data ?? [];
});

const selectedAccount = computed<TUserAccountResource | undefined>(() => {
  return accounts.value?.find(
    (account) => account.id === props.cardForIssue.accountId
  );
});

const exchangeRates = computed(() => {
  return ratesData.value?.data ?? null;
});

const cardBuyBody = computed<UseUserCardBuyPostReq>(() => ({
  account_id: props.cardForIssue.accountId!,
  type: cardBuyBodyType.value,
  start_balance: String(props.cardForIssue.startBalance ?? 0),
  description: props.cardForIssue.description,
  system: Number(props.cardForIssue.system),
  bin:
    isNil(props.cardForIssue.bin) || props.cardForIssue.bin === ""
      ? null
      : Number(props.cardForIssue.bin),
  with_error_data: true,
}));

const {
  isFetching: isFetchingSummary,
  // payment
  tariffPaymentWithDiscount,
  tariffPaymentCrossed,
  promoCodeDiscountPercent,
  // starting balance
  startingBalance,
  startingBalanceCrossed,
  promoCodeBonusAmount,
  // top-up fee
  feeTopUpPercent,
  // total
  totalAmount,
  bonusAmount,
  extraSmallTariffPrice,
} = useCreateCardSummary(
  toRef(props, "cardForIssue"),
  selectedAccount,
  toRef(props, "promoCodeData")
);

const { openKycLimitDialog } = useKycLimitDialog();

const selectedAccountIsoCode = computed<IsoCodeNames>(() => {
  return selectedAccount.value
    ? getAccountCurrencyByCurrencyId(selectedAccount.value.currency_id).isoCode
    : IsoCodeNames.USD;
});

const showSubscriptionTariffPrice = computed<boolean>(() => {
  return !subscriptionsStatus.value && !!isAdvWithSubActive.value;
});

const provideError = (
  error: { message?: string; type?: string; field?: string } | null
) => {
  if (error?.type && "KYC_LIMIT" === error.type) {
    openKycLimitDialog("cards");
  } else if (
    error?.field &&
    ["CARD_DEPOSIT_LIMIT", "card_deposit_limit"].includes(error.field)
  ) {
    openKycLimitDialog("deposit");
  } else if (error?.type) {
    errorToIssue.value = t(`errors.card.create.${error.type}`);
  } else if (error?.message) {
    errorToIssue.value = error.message;
  } else {
    errorToIssue.value = t("cards.create.error.common");
  }
};

const multiBuyRequest = async () => {
  isLoading.value = true;

  const body = {
    ...cardBuyBody.value,
    count: Number(props.cardForIssue.count),
  };

  const { data: checkData } = await useUserCardMultiBuyCheckPost(body);

  if (!checkData.value?.success) {
    provideError(checkData.value);
    isLoading.value = false;
    return;
  }

  const { data } = await useUserCardMultiBuyPost(body);

  if (!data.value?.success) {
    provideError(data.value as { type?: string; field?: string });
    isLoading.value = false;
    return;
  }

  emit("multiBuySuccess");

  isLoading.value = false;

  tracker.logEvent(TrackerEvent.CARD_ISSUED, {
    slug: props.cardForIssue.type,
    count: props.cardForIssue.count,
  });
};

const singleBuyRequest = async () => {
  isLoading.value = true;

  if (props.promoCodeData?.code) {
    const { data } = await usePromoCodeAttachPost(props.promoCodeData.code);

    if (!data.value?.success) {
      errorToIssue.value = t("cards.create.error.promocode_server_error");
      isLoading.value = false;
      return;
    }
  }

  const { data } = await useUserCardBuyPost(cardBuyBody.value);

  if (!data.value?.data) {
    provideError(data.value);
    isLoading.value = false;
    return;
  }

  isLoading.value = false;

  emit("singleBuySuccess", data.value.data);

  tracker.logEvent(TrackerEvent.CARD_ISSUED, {
    slug: props.cardForIssue.type,
  });
};

const onSubmit = async () => {
  if (props.cardForIssue.count > 1) {
    await multiBuyRequest();
  } else {
    await singleBuyRequest();
  }
};
</script>

<template>
  <UITransition>
    <Loader
      v-if="
        isFetchingSummary || isFetchingAccounts || isFetchingExchangeRates
      " />
    <div class="card-approve-form">
      <!-- Title -->
      <div class="w-full">
        <UI2Title
          :title="$t('Confirm your transfer')"
          :subtitle="t('create-card.confirm-subtitle')" />
      </div>

      <!-- Account -->
      <div
        v-if="accounts && exchangeRates"
        class="w-full">
        <UI2Text
          type="body-s"
          weight="semi-medium">
          {{ $t("label.paymentMethod") }}
        </UI2Text>

        <AccountsSelectV2
          class="mt-3"
          :accounts="accounts"
          :model-value="selectedAccount"
          :rates="exchangeRates"
          disabled />
      </div>

      <!-- Total -->
      <CreateCardSummaryV2
        :card-tariff-slug="props.cardForIssue.type"
        :account-iso-code="selectedAccountIsoCode"
        :payment="tariffPaymentWithDiscount"
        :payment-crossed="tariffPaymentCrossed"
        :payment-discount-percent="promoCodeDiscountPercent"
        :starting-balance="startingBalance"
        :starting-balance-bonus="promoCodeBonusAmount"
        :starting-balance-crossed="startingBalanceCrossed"
        :top-up-fee="feeTopUpPercent"
        :is-adv-with-sub-active="
          showSubscriptionTariffPrice && !cardForIssue.type.includes('ultima')
        "
        :subscription-tariff-price="extraSmallTariffPrice"
        :bonus-amount="bonusAmount"
        :total="totalAmount" />

      <!-- Submit -->
      <div class="flex flex-col items-center w-full space-y-4">
        <span
          v-if="errorToIssue"
          class="text-sm text-fg-accent-red"
          data-cy="errorToIssue">
          {{ errorToIssue }}
        </span>

        <UI2Button
          class="w-full"
          :disabled="isLoading"
          :loading="isLoading"
          size="l"
          data-cy="order_button"
          @click="onSubmit">
          {{ $t("Confirm") }}
        </UI2Button>
      </div>
    </div>
  </UITransition>
</template>

<style lang="scss" scoped>
.card-approve-form {
  @apply flex flex-col gap-10 max-w-[33.75rem] px-5 py-10 mx-auto;
}
</style>
