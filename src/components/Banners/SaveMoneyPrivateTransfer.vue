<template>
  <div
    class="wrapper bg-bg-level-1 w-full rounded flex flex-col items-center p-5">
    <!-- Header -->
    <div class="flex flex-none mb-6">
      <p
        class="text-left text-fg-primary text-2xl not-italic font-medium leading-7">
        {{
          $t("transfer.final-force.text", {
            p: calcSaveMoneyPercent.toFixed(2),
          })
        }}
      </p>
    </div>

    <!-- Content -->
    <div class="flex flex-none flex-col bg-white rounded box-border p-4 w-full">
      <div class="flex flex-none flex-col">
        <!-- Top block -->
        <div class="flex flex-none flex-col">
          <div class="flex flex-none flex-row">
            <div class="flex flex-auto">
              <span
                class="text-fg-primary text-lg not-italic font-medium leading-5"
                >{{ $t("transfer.final-force.check-total") }}</span
              >
            </div>
            <div class="flex flex-none none flex-row">
              <div class="flex flex-none">
                <span
                  class="text-fg-secondary text-lg not-italic font-medium leading-5 line-through mr-2"
                  >{{ props.topUpFee }} {{ props.feeCurrencySymbol }}</span
                >
              </div>
              <div class="flex flex-none">
                <span
                  class="text-fg-primary text-lg not-italic font-medium leading-5">
                  {{
                    prepareAccountBalance(
                      topUpFeePrivate,
                      props.feeCurrency,
                      true
                    )
                  }}

                  {{ props.feeCurrencySymbol }}</span
                >
              </div>
            </div>
          </div>
          <div class="flex flex-none">
            <span class="text-fg-green text-sm not-italic font-medium leading-4"
              >{{ $t("transfer.final-force.check-total-economy") }}
              {{ calcSaveMoneyPercent.toFixed(2) }}%</span
            >
          </div>
        </div>

        <!-- Divider -->
        <div class="flex flex-none w-full mt-5 mb-5">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="356"
            height="2"
            viewBox="0 0 356 2"
            fill="none">
            <path
              d="M0 1H356"
              stroke="#B8BECC"
              stroke-dasharray="1 6" />
          </svg>
        </div>

        <!-- List items -->
        <div class="flex flex-col flex-none">
          <!-- check-stat-balance -->
          <div class="flex flex-none flex-row mb-2">
            <div class="flex flex-auto">
              <span
                class="text-fg-secondary text-base not-italic font-normal leading-5"
                >{{ $t("transfer.final-force.check-transfer-amount") }}</span
              >
            </div>
            <div class="flex flex-none">
              <span
                class="text-fg-primary text-base not-italic font-normal leading-5">
                {{
                  prepareAccountBalance(props.amount, props.feeCurrency, true)
                }}

                {{ props.feeCurrencySymbol }}</span
              >
            </div>
          </div>

          <!-- check-top-up-fee -->
          <div class="flex flex-none flex-col mb-2">
            <div class="flex flex-none flex-row">
              <div class="flex flex-auto">
                <span
                  class="text-fg-secondary text-base not-italic font-normal leading-5"
                  >{{ $t("transfer.final-force.check-top-up-fee") }}</span
                >
              </div>
              <div class="flex flex-none">
                <span
                  class="line-through text-fg-secondary text-base not-italic font-normal leading-5 mr-2"
                  >{{ props.topUpFee }} {{ props.feeCurrencySymbol }}</span
                >
                <span
                  class="text-fg-primary text-base not-italic font-normal leading-5">
                  {{
                    prepareAccountBalance(
                      topUpFeePrivate,
                      props.feeCurrency,
                      true
                    )
                  }}
                  {{ props.feeCurrencySymbol }}</span
                >
              </div>
            </div>
            <div class="flex flex-none">
              <span
                class="text-fg-secondary text-base not-italic font-normal leading-5">
                <span class="line-through">{{ props.topUpFeePercent }}%</span>
                {{ PrivateTariffValues.feeTopUpPercent }}%
              </span>
            </div>
            <div class="flex flex-none">
              <span
                class="text-fg-green text-sm not-italic font-medium leading-4"
                >{{ $t("card-issue.final-force.check-top-up-fee-low") }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Button -->
    <div class="flex flex-none -mt-2.5 btn-private">
      <UIButton
        size="m"
        color="green-solid"
        class="w-full"
        icon="private-fill"
        @click="goToPrivate()">
        <template #left>
          <DynamicIcon name="private-fill" />
        </template>
        <span
          class="text-fg-contrast text-center text-base not-italic font-medium leading-5">
          {{ $t("button.subscribePrivate") }}
        </span>
      </UIButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
// import { SupportWidgetV2 } from "@/components/Support";
import { PrivateTariffValues } from "@/constants/private_tariff_values";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { prepareAccountBalance } from "@/helpers";
import type { TCurrenciesCode } from "@/types/dictionary/currencies";
import { TrackerEvent, useTracker } from "@/composable";

const router = useRouter();
const tracker = useTracker();

const topUpFeePrivate = computed(() => {
  return (
    (PrivateTariffValues.feeTopUpPercent * getTopUpFee()) / getTopUpFeePercent()
  );
});

const calcSaveMoneyPercent = computed<number>(() => {
  return 100 - (100 * topUpFeePrivate.value) / getTopUpFee();
});

const getTopUpFee = (): number => {
  return Number(props.topUpFee) || 0;
};
const getTopUpFeePercent = (): number => {
  return props.topUpFeePercent || 0;
};

interface Props {
  amount: number;
  topUpFee: number | string;
  topUpFeePercent: number;
  feeCurrency: TCurrenciesCode;
  feeCurrencySymbol: string;
}

const props = withDefaults(defineProps<Props>(), {
  amount: 0,
  topUpFee: 0,
  topUpFeePercent: 0,
  feeCurrency: "USDT",
  feeCurrencySymbol: "₮",
});

const goToPrivate = () => {
  tracker.logEvent(TrackerEvent.JOIN_PRIVATE_BUTTON_V1, {});
  router.push({ name: "cashback.promo" });
};
</script>

<style lang="scss" scoped>
.btn-private {
  width: calc(100% + 12px);
}
.support-widget {
  position: absolute;
  bottom: 20px;
  right: 15px;
}
</style>
