<template>
  <div class="w-full">
    <div>
      <img
        :src="'/img/subscriptions/hero-1.png'"
        class="w-full" />
    </div>
    <div
      class="wrapper bg-bg-level-1 w-full rounded flex flex-col items-left p-5">
      <!-- Header -->
      <div class="flex flex-none mb-6">
        <p
          class="text-left text-fg-primary text-4 not-italic font-medium leading-7">
          {{ $t("private.banners.saveMoney.youCouldSave") }}
        </p>
      </div>

      <!-- Content -->
      <div class="flex flex-none flex-col rounded box-border items-left w-full">
        <div class="flex flex-none flex-col">
          <!-- List items -->
          <div class="flex flex-col flex-none">
            <div class="flex flex-none flex-col mb-4">
              <div class="flex flex-none">
                <span
                  class="text-fg-secondary text-[2rem] not-italic font-normal leading-7 mr-2 line-through">
                  {{ parseInt(userTariff?.fee_topup) }}%
                </span>
                <span
                  class="text-fg-primary text-[2rem] not-italic font-normal leading-7"
                  >{{ PrivateTariffValues.feeTopUpPercent }}%</span
                >
              </div>

              <div class="flex flex-none text-left mt-2">
                <span
                  class="text-fg-secondary text-3 not-italic font-normal leading-5"
                  >{{ $t("private.banners.saveMoney.withThe") }}
                  <a
                    class="private-link"
                    @click.prevent="goToPrivate()"
                    >Private</a
                  >
                  {{ $t("private.banners.saveMoney.textCompensate") }}
                </span>
              </div>
            </div>

            <div class="flex flex-none flex-col mb-4">
              <div class="flex flex-none">
                <span
                  class="text-fg-secondary text-[2rem] not-italic font-normal leading-7 mr-2 line-through">
                  <template v-if="percentDiscountCostCardPromoCodeValue">
                    {{ cardPriceFeeWithDiscountValue }} $
                  </template>
                  <template v-else> {{ cardPriceFeeValue }} $ </template>
                </span>
                <span
                  class="text-fg-primary text-[2rem] not-italic font-normal leading-7"
                  >{{ PrivateTariffValues.monthlyPayment.toFixed(2) }} $</span
                >
              </div>

              <div class="flex flex-none mt-2">
                <span
                  class="text-fg-secondary text-3 not-italic font-normal leading-5"
                  >{{ $t("private.banners.saveMoney.withThe") }}
                  <a
                    class="private-link"
                    @click.prevent="goToPrivate()"
                    >Private</a
                  >
                  {{ $t("private.banners.saveMoney.textGetCardsCheaper") }}
                </span>
              </div>
            </div>

            <div class="flex flex-none flex-col mb-4 text-left">
              <div class="flex flex-none">
                <span
                  class="text-fg-primary text-[2rem] not-italic font-normal leading-7"
                  >{{ $t("private.banners.saveMoney.textUpTo", { t: 3000 }) }}
                </span>
              </div>

              <div class="flex flex-none mt-2">
                <span
                  class="text-fg-secondary text-3 not-italic font-normal leading-5"
                  >{{ $t("private.banners.saveMoney.textRealMoney") }}
                  <a
                    class="private-link"
                    @click.prevent="goToPrivate()"
                    >Private</a
                  >
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Button -->
    <div class="flex flex-none mt-5 btn-private">
      <UIButton
        size="m"
        color="black"
        class="w-full"
        icon="private-fill"
        @click="goToPrivate()">
        <span
          class="text-fg-contrast text-center text-base not-italic font-medium leading-5">
          {{ $t("card-issue.final-force.button") }}
        </span>
      </UIButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import type { TCardTariff } from "@/types/user/user.types";
import type { TIsoCode } from "@/composable/useAccounts";
import { UserService } from "@modules/services/user";
import { useRouter } from "vue-router";
import { PrivateTariffValues } from "@/constants/private_tariff_values";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { TrackerEvent, useTracker } from "@/composable";

const router = useRouter();
const tracker = useTracker();

interface Props {
  balance: number;
  userTariff: TCardTariff;
  countCards: number;
  percentDiscountCostCardPromoCode: number;
  addRate: TIsoCode | undefined;
  fee?: number;
}

const props = defineProps<Props>();

const rates = ref();
if (props.addRate) {
  UserService.exchangeRates().then((res) => {
    if (res.status) {
      rates.value = res.data;
    }
  });
}

const calcDiscount = (price: number, discountPercent: number): number => {
  return price - (price * discountPercent) / 100;
};

const getCardPrice = (): number => {
  if (!props.userTariff?.card_price) {
    return 0;
  }
  const cardPrice = parseFloat(props.userTariff?.card_price);
  return calcDiscount(cardPrice, percentDiscountCostCardPromoCodeValue.value);
};

const percentDiscountCostCardPromoCodeValue = computed<number>(() => {
  if (countCardsValue.value > 1) return 0;
  return props.percentDiscountCostCardPromoCode || 0;
});

const countCardsValue = computed<number>(() => {
  return props.countCards || 1;
});

const cardPriceFeeWithDiscountValue = computed<number | undefined>(() => {
  if (!percentDiscountCostCardPromoCodeValue.value) return 0;
  const cardPrice = getCardPrice();
  if (!cardPrice) return 0;
  return cardPrice;
});

const cardPriceFeeValue = computed<string | undefined>(() => {
  if (props.userTariff?.card_price === undefined) {
    return undefined;
  }
  const cardPrice = props.userTariff?.card_price;
  if (cardPrice) {
    return parseFloat(cardPrice).toFixed(2);
  }
  return "0";
});

const goToPrivate = () => {
  tracker.logEvent(TrackerEvent.JOIN_PRIVATE_BUTTON_V1, {});
  router.push({ name: "cashback.promo" });
};
</script>

<style lang="scss" scoped>
.btn-private {
  width: calc(100% + 12px);
}
.private-link {
  @apply border-b-[black] border-b border-solid text-fg-primary cursor-pointer;
}
</style>
