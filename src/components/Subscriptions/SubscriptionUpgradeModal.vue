<script setup lang="ts">
import { computed, onUnmounted, onMounted, ref, watchEffect } from "vue";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import { prepareAccountBalance } from "@/helpers/account";
import { maxBy } from "lodash";
import { useI18n } from "vue-i18n";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useSubscriptionsUpgradeInfoPost } from "@/composable/API/useSubscriptionsUpgradeInfoPost";
import { useSubscriptionsUpgradePost } from "@/composable/API/useSubscriptionsUpgradePost";
import { useSubscriptionsDowngradePost } from "@/composable/API/useSubscriptionsDowngradePost";
import type { TSubscriptionUpgradeInfoResource } from "@/types/api/TSubscriptionUpgradeInfoResource";
import UiLoader from "@/components/ui/Loader/Loader.vue";
import {
  getCurrencySymbolByIso,
  type TIsoCode,
  type TypeExchangeRates,
  useDeposit,
  useAccountGet,
  useUserExchangeRatesGet,
  usePromoCodeAttachPost,
  useTracker,
  TrackerEvent,
  useModalStack,
} from "@/composable";
import UIDialog from "@/components/ui/UIDialog/UIDialog.vue";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import AccountsAndCardsSelect from "@/components/AccountsAndCardsSelect/AccountsAndCardsSelect.vue";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import { IsoCodeNames } from "@/constants/iso_code_names";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import { useRoute, useRouter } from "vue-router";
import PromoCodeInput from "../PromoCodeInput/PromoCodeInput.vue";
import { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import { isSubscriptionCodeResource } from "@/components/PromoCodeInput/usePromoCodeInput";
import Calculation from "@/components/Calculation/Calculation.vue";
import CalculationDivider from "../CalculationDivider/CalculationDivider.vue";
import CalculationExchangeRow from "../CalculationExchangeRow/CalculationExchangeRow.vue";
import CalculationTotalRow from "../CalculationTotalRow/CalculationTotalRow.vue";
import CalculationValueRow from "../CalculationValueRow/CalculationValueRow.vue";
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";

const props = defineProps<{
  currentTariff: TSubscriptionTariffResource;
  newTariff: TSubscriptionTariffResource;
  extentionsAmount?: number | null;
  isUpgrade: boolean;
}>();

const emit = defineEmits<{
  close: [];
  onSuccess: [];
}>();

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const tracker = useTracker();
const { data: accounts, execute: fetchAccounts } = useAccountGet();
const { data: ratesData } = useUserExchangeRatesGet();
const { setState, replaceQuery } = useDeposit();
const { isSuspicious } = storeToRefs(useUserStore());
const { openModal } = useModalStack();

const selectedAccount = ref<TUserAccountResource | null>(null);

const isLoading = ref<boolean>(true);

const rates = computed<TypeExchangeRates | null>(() => {
  return ratesData.value?.data ?? null;
});

const uiError = ref<string>("");
const currentAccountId = ref<number | null>(null);
const intervalUpdateUserExchangeRates = ref();

const updateAccounts = async () => {
  await fetchAccounts();
  // Update selected account for update all computeds
  changeAccountHandler(currentAccountId.value);
};

const INTERVAL_FOR_UPDATING_ACCOUNT_BALANCES_SEC = 10;
onMounted(() => {
  intervalUpdateUserExchangeRates.value = setInterval(() => {
    updateAccounts();
  }, INTERVAL_FOR_UPDATING_ACCOUNT_BALANCES_SEC * 1000);
});

onUnmounted(() => {
  clearInterval(intervalUpdateUserExchangeRates.value);
});

const currentAccount = (id: number) => {
  return accounts.value?.data?.find((item) => item.id === id);
};

const usdtAccountId = computed(() => {
  return accounts.value?.data?.find(
    (item) =>
      getAccountCurrencyByCurrencyId(item.currency_id).isoCode ===
      IsoCodeNames.USDT
  );
});

const isoCodeUsdOrEur = computed(() => {
  if (!accountIsoCode.value) return false;
  return ["USD", "EUR"].includes(accountIsoCode.value);
});

const changeAccountHandler = (v: any): void => {
  uiError.value = "";
  currentAccountId.value = v;
  selectedAccount.value = currentAccount(v) || null;
};

const accountIsoCode = computed<TIsoCode | null>(() => {
  if (selectedAccount.value) {
    return getAccountCurrencyByCurrencyId(selectedAccount.value.currency_id)
      .isoCode;
  }
  return null;
});

const buyReqIsLoading = ref<boolean>(false);

const upgradeReq = () => {
  return useSubscriptionsUpgradePost({
    subscription_tariff_id: props.newTariff?.id,
    user_account_id: Number(currentAccountId.value),
  });
};

const downgradeReq = () => {
  return useSubscriptionsDowngradePost({
    subscription_tariff_id: props.newTariff?.id,
    user_account_id: Number(currentAccountId.value),
  });
};

const openModalTopUpAccountBalance = () => {
  openModal("transactions", {
    type: "deposit",
    toDirection: "account",
    toId: Number(isoCodeUsdOrEur.value ? usdtAccountId : currentAccountId),
  });
};

const promoCode = ref<TPromoCodeResource | null>(null);

const setPromoCode = (code: TPromoCodeResource | undefined) => {
  promoCode.value = code ?? null;
};

const discountPercent = computed(() =>
  promoCode.value && isSubscriptionCodeResource(promoCode.value.fields)
    ? Number(promoCode.value.fields.subscription_purchase_discount_percent)
    : 0
);

const submitUpgradeForm = async () => {
  if (isUpgradeBtnDisabled.value) return;

  uiError.value = "";
  buyReqIsLoading.value = true;

  if (promoCode.value) {
    const { data: attachResponse } = await usePromoCodeAttachPost(
      promoCode.value.code
    );

    if (!attachResponse.value?.success) {
      uiError.value = "errors.subscription-purchase-error";
      return;
    }
  }

  const { data } = props.isUpgrade ? await upgradeReq() : await downgradeReq();
  if (data.value?.data) {
    tracker.logEvent(TrackerEvent.USER_UPGRADED_SUBSCRIPTION, {});
    emit("onSuccess");
  } else {
    const errMsg = data.value?.error?.full_code
      ? t(`errors.${data.value.error.full_code}`)
      : t("tariff.upgradeTariff.errorGetInfo");
    uiError.value = errMsg;
  }
  buyReqIsLoading.value = false;
};

const accountWithMaxUsd = computed(() => {
  const mapAccounts = accounts.value?.data?.map((account) => {
    return {
      id: account.id,
      balance: Number(account.balance_default),
    };
  });
  return maxBy(mapAccounts, "balance");
});

const subTitleModal = computed<string>(() => {
  return `${props.newTariff?.name}  —  ${props.newTariff?.cards_limit} ${t(
    "subscriptions.modal.changeTariff.cards"
  )}`;
});

const tariffUpgradeInfoHaveDiscount = computed<boolean>(() => {
  return !!Number(tariffUpgradeInfo.value?.discount);
});

const tariffUpgradeInfo = ref<TSubscriptionUpgradeInfoResource>();

// Amount without currency convert
const totalUpgradeAmount = computed(() => {
  if (props.isUpgrade) {
    return Number(tariffUpgradeInfo.value?.upgradeAmount);
  } else {
    const newTariffAmount = Number(props.newTariff.amount) || 0;
    const newTariffAmountExtensions = Number(props.extentionsAmount) || 0;
    return newTariffAmount + newTariffAmountExtensions;
  }
});

// Converted for currency
const totalUpgradeAmountCurrency = computed(() => {
  let total = totalUpgradeAmount.value;
  const isoCode = accountIsoCode.value as TIsoCode;
  const actualRates = rates.value;

  if (!total || !isoCode || !actualRates) return "";

  if (isoCode !== "USD") {
    total = total / Number(actualRates[isoCode]["USD"]);
  }

  return prepareAccountBalance(total, isoCode);
});

// Converted amount with discount (Total)
const totalUpgradeAmountCurrencyWithDiscount = computed(() => {
  return (
    (Number(totalUpgradeAmountCurrency.value) * (100 - discountPercent.value)) /
    100
  );
});

// Formatted amount for displaying in template
const totalAmountWithSymbol = computed(() => {
  const isoCode = accountIsoCode.value as TIsoCode;
  return `${prepareAccountBalance(
    totalUpgradeAmountCurrencyWithDiscount.value,
    isoCode
  )} ${getCurrencySymbolByIso(isoCode)}`;
});

// Check is enough money on balance for payment
const isSelectedAccountWithBalance = computed<boolean>(() => {
  return (
    Number(selectedAccount.value?.balance || "0") >=
    Number(totalUpgradeAmountCurrencyWithDiscount.value)
  );
});

// Not enough money, but user can't top up account
// In that case display disabled Upgrade button
const isUpgradeBtnDisabled = computed(() => {
  return !isSelectedAccountWithBalance.value && isSuspicious.value;
});

const isOpenModalAvoidCurrencyConversion = ref(false);
const closeModalAvoidCurrencyConversion = () => {
  isOpenModalAvoidCurrencyConversion.value = false;
};

const errorNotEnoughMoney = computed(() => {
  if (isLoading.value) return "";
  if (!isSelectedAccountWithBalance.value) return t("errors.not-enough-money");
  return "";
});

watchEffect(async () => {
  if (!currentAccountId.value && accountWithMaxUsd.value && rates.value) {
    changeAccountHandler(accountWithMaxUsd.value.id);

    isLoading.value = true;
    const { data } = await useSubscriptionsUpgradeInfoPost({
      subscription_tariff_id: props.newTariff?.id,
    });

    isLoading.value = false;

    if (!data.value?.data) {
      const errMsg = data.value?.error?.full_code
        ? t(`errors.${data.value.error.full_code}`)
        : t("tariff.upgradeTariff.errorGetInfo");

      uiError.value = errMsg;
      return;
    }

    tariffUpgradeInfo.value = data.value?.data;
  }
});
</script>

<template>
  <UIDialog
    v-if="isOpenModalAvoidCurrencyConversion"
    :callback-confirm="closeModalAvoidCurrencyConversion"
    :show-btn-cancel="false"
    :is-open="isOpenModalAvoidCurrencyConversion"
    :title="$t('pst-private.how-to-avoid-currency-conversion')"
    :text="$t('pst-private.how-to-avoid-conversion-ach-payment')"
    @close="closeModalAvoidCurrencyConversion" />

  <UIFullScreenModal
    :is-open="true"
    :title="`${$t('subscriptions.modal.changeTariff.purchase')}&nbsp;Private`"
    :subtitle="subTitleModal"
    @close="$emit('close')">
    <template #content>
      <div
        v-if="accounts && rates"
        class="m-auto w-full max-w-[28.75rem] bg-white flex flex-col mt-10">
        <h2 class="text-xl lg:text-5 font-medium mb-10">
          {{ $t("Confirm your purchase") }}
        </h2>

        <div class="mb-10 w-full">
          <AccountsAndCardsSelect
            :rates="rates"
            data-testid="accounts-and-cards-select-upgrade"
            :accounts="accounts.data"
            :error="errorNotEnoughMoney"
            :model-value="currentAccountId"
            :tariffs="[]"
            @update:model-value="changeAccountHandler" />
        </div>

        <Calculation
          v-if="!isLoading"
          class="mb-2.5">
          <CalculationValueRow :title="`Private ${newTariff?.name || ''}`">
            <span
              v-if="isUpgrade && tariffUpgradeInfoHaveDiscount"
              class="strikethrough mr-2">
              {{ tariffUpgradeInfo?.amountTariff }} $
            </span>
            <span class="whitespace-nowrap">
              {{
                isUpgrade
                  ? tariffUpgradeInfo?.amountWithDiscount
                  : parseInt(newTariff.amount)
              }}
              $
            </span>
            <template
              v-if="props.isUpgrade && tariffUpgradeInfoHaveDiscount"
              #subtitle>
              <span class="text-fg-green text-3.5 font-medium leading-4">
                {{
                  $t("subscriptions.modal.changeTariff.discountUnusedDays", {
                    t: props.currentTariff.name,
                  })
                }}
              </span>
            </template>
          </CalculationValueRow>

          <CalculationValueRow
            :title="t('tariff.upgradeTariff.currentExtensionPacks')">
            <template
              v-if="props.isUpgrade && tariffUpgradeInfo?.amountExtensions">
              {{ tariffUpgradeInfo?.amountExtensions }} $
            </template>
            <template
              v-else-if="
                !props.isUpgrade &&
                props.extentionsAmount &&
                props.extentionsAmount > 0
              ">
              {{ props.extentionsAmount }} $
            </template>
          </CalculationValueRow>

          <CalculationExchangeRow
            v-if="accountIsoCode && accountIsoCode !== 'USD'"
            :value="`1 ${accountIsoCode} = ${prepareAccountBalance(
              Number(rates[accountIsoCode]['USD']),
              'USD'
            )} ${IsoCodeNames.USD}`" />

          <CalculationDivider />

          <div>
            <CalculationTotalRow
              :title="t('subscriptions.modal.changeTariff.total')"
              :value="totalAmountWithSymbol" />

            <div
              v-if="discountPercent"
              class="flex justify-between">
              <div class="text-fg-green font-medium text-3.5 leading-4 mt-1">
                {{ $t("operations.promocode") }}
                {{ discountPercent }}%
              </div>
              <div class="strikethrough">
                {{ totalUpgradeAmountCurrency }}
              </div>
            </div>
          </div>
        </Calculation>

        <PromoCodeInput
          v-if="isUpgrade"
          class="mb-10"
          mode="subscription"
          @set-promo-code="setPromoCode" />

        <div
          v-if="!isLoading"
          class="w-full flex flex-col">
          <!-- Buy button  -->
          <UIButton
            v-if="isSelectedAccountWithBalance || isSuspicious"
            :is-loading="buyReqIsLoading"
            size="m"
            type="button"
            color="black"
            class="col-span-2 mb-6 min-w-full font-medium"
            data-testid="upgrade-subscription-btn"
            :disabled="buyReqIsLoading || isUpgradeBtnDisabled"
            @click="submitUpgradeForm">
            <span class="font-medium text-white text-4 leading-5">
              {{
                $t("subscriptions.modal.changeTariff.connectBtn") +
                " Private " +
                newTariff?.name
              }}
            </span>
          </UIButton>
          <UIButton
            v-else
            size="m"
            type="button"
            color="orange-solid"
            class="col-span-2 mb-6 min-w-full font-medium"
            @click="openModalTopUpAccountBalance">
            <template #left>
              <DynamicIcon name="plus" />
            </template>
            <span class="font-medium text-white text-4 leading-5">
              {{ $t("deposit.topUpAccount") }}
            </span>
          </UIButton>

          <p
            v-if="uiError"
            class="text-normal text-fg-red text-center">
            {{ $t(`${uiError}`) }}
          </p>
        </div>
      </div>
      <UiLoader v-else />
    </template>
  </UIFullScreenModal>
</template>

<style lang="scss" scoped>
.strikethrough {
  @apply text-fg-secondary line-through whitespace-nowrap;
}
</style>
