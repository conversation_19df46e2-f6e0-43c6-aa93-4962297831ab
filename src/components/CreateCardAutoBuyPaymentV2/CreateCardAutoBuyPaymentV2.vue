<script lang="ts" setup>
import { computed, onUnmounted, ref, toRef, watch } from "vue";
import { useCreateCardSummary } from "@/components/CreateCardV2/useCreateCardSummary";
import UITransition from "@/components/ui/UITransition.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { useUserStore } from "@/stores/user";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import {
  checkAndCreateAutoBuy,
  checkAutoBuyTimer,
  checkAutoBuyTransaction,
} from "@/composable/CardAutoBuy";
import type { IAutoBayStoreDto } from "@modules/services/autoBuy";
import {
  useAccountGet,
  useSubscriptionPlusCardAutobuy1Experiment,
  useTracker,
  TrackerEvent,
} from "@/composable";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import UI2Title from "@/components/ui2/UI2Title/UI2Title.vue";
import NetworkTabs from "@/components/NetworkTabs/NetworkTabs.vue";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import TopUpQrCode from "@/components/TopUpQrCode/TopUpQrCode.vue";
import UI2CopyBlock from "@/components/ui2/UI2CopyBlock/UI2CopyBlock.vue";
import UI2MessageBlock from "@/components/ui2/UI2MessageBlock/UI2MessageBlock.vue";
import { TTopUpStatusCode } from "@/components/TopUpQrCode/types";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import CreateCardSummaryV2 from "@/components/CreateCardSummaryV2/CreateCardSummaryV2.vue";
import PromoCodeInputV2 from "@/components/PromoCodeInputV2/PromoCodeInputV2.vue";

const props = defineProps<{
  cardForIssue: TCardForIssue;
  promoCodeData: TPromoCodeResource | null;
}>();

const emit = defineEmits<{
  autoBuySuccess: [];
  setPromoCode: [promoCodeData: TPromoCodeResource | null];
}>();

const { t, formatCurrency } = useI18nWrapper();
const userStore = useUserStore();
const { subscriptionsStatus } = useSubscriptionsInfo();
const { data: accountsData, isFetching: isFetchingAccounts } = useAccountGet();
const { getValue } = useSubscriptionPlusCardAutobuy1Experiment();
const { isActive: isAdvWithSubActive } = getValue();
const tracker = useTracker();

const accounts = computed<TUserAccountResource[]>(() => {
  return accountsData.value?.data ?? [];
});

const transactionCheckValue = ref<string>("");
const autoBuyId = ref<number | null>(null);
const currentTopUpStatus = ref<TTopUpStatusCode>(TTopUpStatusCode.PENDING);
const autoBuyCheckTimer = ref<ReturnType<typeof setInterval> | null>(null);
const transactionCheckTimer = ref<ReturnType<typeof setInterval>>();
const selectedNetworkAccount = ref<TUserAccountResource | null>(null);

const {
  isFetching: isFetchingSummary,
  // payment
  tariffPaymentWithDiscount,
  tariffPaymentCrossed,
  promoCodeDiscountPercent,
  // starting balance
  startingBalance,
  startingBalanceCrossed,
  promoCodeBonusAmount,
  // top-up fee
  feeTopUpPercent,
  // total
  totalAmount,
  bonusAmount,
  extraSmallTariffPrice,
} = useCreateCardSummary(
  toRef(props, "cardForIssue"),
  selectedNetworkAccount,
  toRef(props, "promoCodeData")
);

const showSubscriptionTariffPrice = computed<boolean>(() => {
  return !subscriptionsStatus.value && !!isAdvWithSubActive.value;
});

const promoCode = computed<string>(() => {
  return props.promoCodeData?.code ?? "";
});

// only for adv or ultima monthly tariffs
const isPromoCodeWidgetVisible = computed(() => {
  const isUltimaMonthlyTariff =
    props.cardForIssue.type.includes("ultima") &&
    ["ultima-3ds", "ultima"].includes(props.cardForIssue.type);

  return isUltimaMonthlyTariff && props.cardForIssue.count === 1;
});

const autoBuyPayload = computed<IAutoBayStoreDto>(() => {
  return {
    type: props.cardForIssue.type,
    start_balance: String(props.cardForIssue.startBalance ?? 0),
    code: promoCode.value,
    system: Number(props.cardForIssue.system),
  };
});

const setAutoBuy = async () => {
  const autoBuy = await checkAndCreateAutoBuy(autoBuyPayload.value);

  if (!autoBuy?.status) {
    currentTopUpStatus.value = TTopUpStatusCode.ERROR;
    return;
  }

  if (promoCode.value) {
    await tracker.logEvent(TrackerEvent.PROMOCODE_ACTIVATE, {
      code: promoCode.value,
    });

    await tracker.setUserProperty(TrackerEvent.PROMOCODE_USED, promoCode.value);
  }

  autoBuyId.value = autoBuy.data?.id ?? null;

  autoBuyCheckTimer.value = checkAutoBuyTimer(
    autoBuy.data?.id!,
    (status: TTopUpStatusCode) => {
      switch (status) {
        case TTopUpStatusCode.SUCCESS:
          clearInterval(autoBuyCheckTimer.value!);
          currentTopUpStatus.value = TTopUpStatusCode.SUCCESS;
          emit("autoBuySuccess");
          break;
        case TTopUpStatusCode.ERROR:
          clearInterval(autoBuyCheckTimer.value!);
          currentTopUpStatus.value = TTopUpStatusCode.ERROR;
          break;
      }
    }
  );

  transactionCheckTimer.value = setInterval(async () => {
    const res = await checkAutoBuyTransaction(
      selectedAccountAddress.value ?? ""
    );
    if (!res.status) {
      clearInterval(transactionCheckTimer.value);
      return;
    }
    transactionCheckValue.value = res.data;
    if (transactionCheckValue.value !== res.data) {
      clearInterval(transactionCheckTimer.value);
      currentTopUpStatus.value = TTopUpStatusCode.PENDING;
    }
  }, 3000);

  tracker.logEvent(TrackerEvent.DEPOSIT_METHOD_SHOW_QR);
};

const clearAllTimers = () => {
  if (autoBuyCheckTimer.value) {
    clearInterval(autoBuyCheckTimer.value);
  }
  if (transactionCheckTimer.value) {
    clearInterval(transactionCheckTimer.value);
  }
};

const resetAutoBuy = () => {
  clearAllTimers();
  setAutoBuy();
};

const cryptoAccounts = computed<TUserAccountResource[]>(() => {
  return (
    accounts.value
      .filter((acc) => {
        const isoCode = getAccountCurrencyByCurrencyId(acc.currency_id).isoCode;
        return isoCode === IsoCodeNames.USDT || isoCode === IsoCodeNames.BTC;
      })
      // Tether USDT first in list
      .sort((a, b) => {
        const aIso = getAccountCurrencyByCurrencyId(a.currency_id).isoCode;
        const bIso = getAccountCurrencyByCurrencyId(b.currency_id).isoCode;

        if (aIso === bIso) return 0;
        if (aIso === IsoCodeNames.USDT) return -1;
        if (bIso === IsoCodeNames.USDT) return 1;

        return 0;
      })
  );
});

const selectedAccountIsoCode = computed<IsoCodeNames | null>(() => {
  if (!selectedNetworkAccount.value) return null;

  return getAccountCurrencyByCurrencyId(
    selectedNetworkAccount.value.currency_id
  ).isoCode;
});

const selectedAccountAddress = computed<string | null>(() => {
  if (
    !selectedNetworkAccount.value ||
    !selectedNetworkAccount.value.addresses?.length ||
    !selectedNetworkAccount.value.addresses[0]?.address
  )
    return null;

  return selectedNetworkAccount.value.addresses[0].address;
});

const isUserFeesUSDT = computed<boolean>(() => {
  return Boolean(Number(userStore.userFees.deposit_fee_usdt));
});

const handleNetworkTabsUpdate = (isoCode: IsoCodeNames) => {
  selectedNetworkAccount.value =
    accounts.value.find((acc) => {
      return (
        getAccountCurrencyByCurrencyId(acc.currency_id).isoCode === isoCode
      );
    }) ?? accounts.value[0];

  tracker.logEvent(TrackerEvent.DEPOSIT_METHOD_AUTOBUY, {
    crypto: selectedAccountIsoCode.value,
  });
};

const setPromoCodeHandler = (data?: TPromoCodeResource) => {
  emit("setPromoCode", data ?? null);
};

onUnmounted(() => {
  clearAllTimers();
});

watch(
  () => autoBuyPayload.value,
  () => {
    resetAutoBuy();
  }
);

watch(isFetchingAccounts, () => {
  if (cryptoAccounts.value) {
    selectedNetworkAccount.value = cryptoAccounts.value[0];
  }
});

setAutoBuy();
userStore.getUserFees();
</script>

<template>
  <UITransition>
    <Loader
      v-if="
        isFetchingSummary || isFetchingAccounts || !selectedAccountIsoCode
      " />
    <div
      v-else
      class="max-w-[31.25rem] w-full mx-auto flex gap-10 flex-col">
      <UI2Title
        :title="t('cards.create.autobuy.title')"
        :subtitle="t('cards.create.autobuy.subtitle')" />
      <div>
        <UI2Text
          class="mb-3"
          type="body-s"
          weight="semi-medium"
          >{{ t("cards.create.autobuy.payment") }}</UI2Text
        >
        <NetworkTabs
          class="mb-1"
          :model-value="selectedAccountIsoCode"
          :accounts="cryptoAccounts"
          @update:model-value="(v) => handleNetworkTabsUpdate(v as IsoCodeNames)" />
        <div class="flex gap-1 items-start text-fg-base-tertiary">
          <DynamicIcon
            class="w-4 h-4 mt-px"
            name="alert-circle" />
          <UI2Text>{{ t("cards.create.autobuy.warn") }}</UI2Text>
        </div>
      </div>
      <div>
        <TopUpQrCode
          v-if="selectedAccountAddress"
          class="mb-2"
          :deposit-status-code="currentTopUpStatus"
          :deposit-address="selectedAccountAddress" />
        <UI2CopyBlock
          :label="t('cards.create.autobuy.copy-total-amount')"
          class="mb-2"
          :source="String(totalAmount)">
          {{ formatCurrency(totalAmount, selectedAccountIsoCode) }}
        </UI2CopyBlock>
        <UI2MessageBlock
          v-if="selectedAccountIsoCode === IsoCodeNames.BTC"
          type="critical"
          :title="t('cards.create.autobuy.message-title')">
          <template #subtitle>
            <div>
              {{ $t("cards.create.autobuy.bitcoin-alert.first") }}
              <br class="hidden md:inline" />
              {{ $t("cards.create.autobuy.bitcoin-alert.second") }}
            </div>
          </template>
        </UI2MessageBlock>
        <UI2MessageBlock
          v-if="selectedAccountIsoCode === IsoCodeNames.USDT && isUserFeesUSDT"
          type="critical"
          :title="t('cards.create.autobuy.message-title')">
          <template #subtitle>
            <div>
              {{
                $t("cards.create.autobuy.tether-alert", [
                  Number(userStore.userFees.deposit_fee_usdt_threshold),
                  Number(userStore.userFees.deposit_fee_usdt),
                ])
              }}
            </div>
          </template>
        </UI2MessageBlock>
      </div>
      <CreateCardSummaryV2
        :card-tariff-slug="cardForIssue.type"
        :account-iso-code="selectedAccountIsoCode"
        :payment="tariffPaymentWithDiscount"
        :payment-crossed="tariffPaymentCrossed"
        :payment-discount-percent="promoCodeDiscountPercent"
        :starting-balance="startingBalance"
        :starting-balance-bonus="promoCodeBonusAmount"
        :starting-balance-crossed="startingBalanceCrossed"
        :top-up-fee="feeTopUpPercent"
        :is-adv-with-sub-active="
          showSubscriptionTariffPrice && !cardForIssue.type.includes('ultima')
        "
        :subscription-tariff-price="extraSmallTariffPrice"
        :bonus-amount="bonusAmount"
        :total="totalAmount" />
      <div
        v-if="isPromoCodeWidgetVisible"
        class="w-full">
        <PromoCodeInputV2
          :model-value="props.promoCodeData"
          mode="card"
          @update:model-value="
            (inputPromoCode) => setPromoCodeHandler(inputPromoCode ?? undefined)
          " />
      </div>
    </div>
  </UITransition>
</template>
