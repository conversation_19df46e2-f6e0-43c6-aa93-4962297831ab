<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import UITableV2 from "@/components/ui/UITableV2/UITableV2.vue";
import BinGridItem from "@/components/CreateCardSelectBin/BinGridItem.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { useSubscriptionsList } from "@/composable/useSubscriptionsList";
import type { TUITableColumn } from "@/components/ui/UITableV2/types";
import type { TBinInfo, TSelectedBin } from "@/components/CreateCardV2/types";
import type { TTariffResource } from "@/types/api/TTariffResource";
import { cardsWithoutCashback, feeTopupPrivate } from "@/config/cards";
import { useRouter } from "vue-router";
import { isMobile, isTablet } from "@/helpers";
import UITooltip from "@/components/ui/UITooltip/UITooltip.vue";
import { useUserStore } from "@/stores/user";
import UIModal from "@/components/ui/UIModal/UIModal.vue";
import { RouteName } from "@/constants/route_name";
import CreateCardSelectBinCellFee from "./CreateCardSelectBinCellFee.vue";
import { useCountrySets } from "@/composable/useCountrySets";
import {
  type TCardTariffSlug,
  useUserSubscriptionsTariffs,
  useVerificationActualGet,
  useTracker,
  TrackerEvent,
} from "@/composable";
import { useKycLimitDialog } from "@/composable/useKycLimitDialog";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import { nonUltimaSubscriptionCardsLimitGuard } from "@/middleware/nonUltimaSubscriptionCardsLimitGuard";

const props = defineProps<{
  isLoading: boolean;
  bins: TBinInfo[];
  selectedTariff: TTariffResource;
  isExperimentalAutobuy: boolean;
}>();

const emit = defineEmits<{
  selectBin: [v: TSelectedBin];
  improveTariff: [bin: string, tariffs?: string[]];
}>();

const { t } = useI18n();
const router = useRouter();
const tracker = useTracker();

const { data: verificationData } = useVerificationActualGet();
const { subscriptionsInfo, subscriptionsStatus } = useSubscriptionsInfo({
  immediate: false,
});
const { userSubscriptionsTariffs } = useUserSubscriptionsTariffs();

const isMonthlyPaymentColumnInvisible = computed(() => {
  return (
    subscriptionsInfo.value?.status_name.toLowerCase() === "active" ||
    props.isExperimentalAutobuy
  );
});

const userStore = useUserStore();
const isMember = computed(
  () => userStore.isTeamMember && userStore.user.type === 3
);
const showMemberDialog = ref(false);

const { currentMainTariff } = useSubscriptionsList({ immediate: false });

const columns = computed<TUITableColumn[]>(() => {
  const recommendedColumn: TUITableColumn = {
    label: t("label.frequentlyUsedFor"),
  };

  const monthlyPaymentColumn: TUITableColumn = {
    label: t("createFirstCard.monthlyPayment"),
    align: "right",
  };

  return [
    {
      label: t("label.bin"),
    },
    {
      label: t("label.paymentSystem"),
    },
    {
      label: t("label.secure"),
      align: "left",
    },
    ...(showRecommended.value ? [recommendedColumn] : []),
    {
      label: t("createFirstCard.transactionFee"),
      align: "right",
    },
    {
      label: t("label.topupFee"),
      align: "right",
    },
    {
      label: t("cashback.title"),
      align: "right",
    },
    ...(isMonthlyPaymentColumnInvisible.value ? [] : [monthlyPaymentColumn]),
    {
      label: "",
      align: "right",
    },
  ];
});

const tableGridClass = computed(() => {
  switch (columns.value.length) {
    case 7:
      return "grid-class-7";
    case 8:
      return "grid-class-8";
    default:
      return "grid-class-9";
  }
});

const cashbackPercent = computed<number>(() => {
  if (
    props.isExperimentalAutobuy &&
    !cardsWithoutCashback.includes(props.selectedTariff.slug)
  ) {
    return 1;
  }

  if (
    cardsWithoutCashback.includes(props.selectedTariff.slug) ||
    !subscriptionsInfo.value?.cashback_percent
  ) {
    return 0;
  }

  return Number(subscriptionsInfo.value.cashback_percent) ?? 0;
});

const subscriptionLevel = computed(() => {
  return currentMainTariff.value?.subscription_tariff.name
    .slice(0, 1)
    .toLowerCase();
});

const fakeCashback = computed<number>(() => {
  return cardsWithoutCashback.includes(props.selectedTariff.slug) ? 0 : 3;
});

const fakeTariff = computed<TTariffResource>(() => ({
  ...props.selectedTariff,
  fee_transaction_amount: "0",
  card_price: "0",
  fee_topup: cardsWithoutCashback.includes(props.selectedTariff.slug)
    ? props.selectedTariff.fee_topup ?? ""
    : String(feeTopupPrivate),
}));

const isNewBin = (item: TBinInfo) => {
  return item.featured && item.bin === "542723";
};

const showRecommended = computed(
  () => props.selectedTariff.slug === "advertisement-cards"
);

/*
TODO: https://pspace.me/p/pst/issues/4069
const availableAtHigherTariff = (item: TBinInfo) => {
  return (
    !!subscriptionLevel.value &&
    !!item.tariffs &&
    !item.tariffs.includes(subscriptionLevel.value)
  );
};
*/

const isAuto3ds = (bin: string): boolean => {
  return ["542723", "517746"].includes(bin);
};

const onSelectBin = async (item: TBinInfo) => {
  if (!item.blackListed) {
    const canSelectBin = item.slug.includes("ultima")
      ? true
      : await nonUltimaSubscriptionCardsLimitGuard();

    if (canSelectBin) {
      emit("selectBin", { bin: item.bin, slug: item.slug as TCardTariffSlug });
    }
  } else if (isMember.value) {
    showMemberDialog.value = true;
  } else {
    await router.push({ name: RouteName.SUBSCRIPTION_TARIFF });
    await tracker.logEvent(TrackerEvent.BUTTON, {
      page: "/app/create",
      page_version: 0,
      name: "Private",
      version: 0,
      location: "table",
    });
  }
};

const cardsByVerificationCount = computed<number | null>(() => {
  return verificationData.value?.data?.remaining_cards ?? null;
});

const { openKycLimitDialog } = useKycLimitDialog();
const { countrySetGuard, isActive: needVerificationAction } = useCountrySets();

const onSelectBinHandle = async (item: TBinInfo) => {
  if (cardsByVerificationCount.value === 0) {
    openKycLimitDialog("cards");
    return;
  }

  if (needVerificationAction.value) {
    await onSelectBin(item);
    return;
  }
  countrySetGuard(
    () => {
      onSelectBin(item);
    },
    async () => {
      await router.push({ name: RouteName.DASHBOARD });
    }
  );
};

const getButtonName = (binInfo: TBinInfo): string => {
  if (binInfo.blackListedLabel) {
    return binInfo.blackListedLabel;
  }

  if (binInfo.blackListed) {
    return "Private";
  }

  return t("Order");
};

const getExperimentalAutobuyPercentByKey = (
  bin: TBinInfo,
  tariffKey: keyof TSubscriptionTariffResource
) => {
  let minPercent = 0;
  let maxPercent = 0;

  if (bin.disabled_for) {
    const disabledFor = bin.disabled_for;
    const cashbackTariffs = userSubscriptionsTariffs.value.filter(
      (item) => !disabledFor.includes(item.name)
    );

    if (cashbackTariffs.length > 1) {
      minPercent = Number(cashbackTariffs[0][tariffKey]);
      maxPercent = Number(cashbackTariffs[1][tariffKey]);
    }
  } else {
    if (userSubscriptionsTariffs.value.length > 1) {
      minPercent = Number(userSubscriptionsTariffs.value[0][tariffKey]);
      maxPercent = Number(
        userSubscriptionsTariffs.value[
          userSubscriptionsTariffs.value.length - 1
        ][tariffKey]
      );
    }
  }

  if (maxPercent === minPercent) {
    return minPercent;
  }

  return tariffKey === "fee_topup"
    ? `${maxPercent}-${minPercent}`
    : `${minPercent}-${maxPercent}`;
};

const getCashbackPercent = (bin: TBinInfo) => {
  if (props.isExperimentalAutobuy) {
    return getExperimentalAutobuyPercentByKey(bin, "cashback_percent");
  }

  return bin.blackListed ? fakeCashback.value : cashbackPercent.value;
};

// TODO: https://pspace.me/p/pst/issues/4069
// const onImproveTariffHandle = (item: TBinInfo) => {
//   emit("improveTariff", item.bin, item.tariffs);
// };
</script>

<template>
  <!-- eslint-disable max-len -->
  <UITableV2
    v-if="!isMobile && !isTablet"
    class="select-bin-table"
    :grid-class="tableGridClass"
    :columns="columns"
    :items="props.bins"
    :is-loading="props.isLoading">
    <template #item="{ item }">
      <!--  BIN  -->
      <template v-if="item?.under_maintenance">
        <UITooltip
          placement="bottom-start"
          :triggers="['hover']">
          <div class="cell">
            <span
              class="flex items-center gap-1 rounded px-1.5 py-px bg-bg-orange-light">
              <DynamicIcon
                v-if="isNewBin(item)"
                class="h-4 w-4 text-core-red-500"
                name="fire" />
              {{ item.bin }}
              <DynamicIcon
                name="warning_circle"
                class="h-4 w-4 text-fg-orange" />
            </span>
          </div>
          <template #popper>
            <div class="max-w-[260px] p-2">
              <span class="text-4 text-white">
                {{ $t("common.technical-work.title") }}
              </span>
              <p class="text-3.5 text-fg-tertiary">
                {{ $t("common.technical-work.descriptions") }}
              </p>
            </div>
          </template>
        </UITooltip>
      </template>
      <div
        v-else
        :class="{ 'new-bin': isNewBin(item) }"
        class="cell">
        <span class="flex items-center gap-1">
          <UITooltip
            v-if="isNewBin(item)"
            popper-class="max-w-[13rem]"
            placement="bottom"
            :hide-triggers="() => ['hover']"
            :triggers="['hover']">
            <span class="cursor-pointer">
              <DynamicIcon
                name="fire"
                class="h-4 w-4 text-core-red-500" />
            </span>
            <template #popper>
              <div class="text-white text-3 leading-4 p-2">
                {{ t("cards.exclusive-bin") }}
              </div>
            </template>
          </UITooltip>
          {{ item.bin }}
        </span>
      </div>

      <!--  Payment System   -->
      <div class="cell justify-start">
        <span class="py-1 w-fit">
          <DynamicIcon
            class="h-4 w-7"
            :name="item.bin.startsWith('4') ? 'visa' : 'mastercard'"
            path="gateway" />
        </span>
      </div>

      <!--  Secure  -->
      <div class="cell justify-start">
        <DynamicIcon
          class="h-4 w-4"
          :name="item.secure || isAuto3ds(item.bin) ? 'check' : 'close'"
          :class="
            item.secure || isAuto3ds(item.bin)
              ? 'text-fg-primary'
              : 'text-fg-tertiary'
          " />

        <template v-if="item.autoSecure || isAuto3ds(item.bin)">
          <div class="leading-4">Auto</div>
          <UITooltip
            popper-class="max-w-[12.5rem]"
            placement="bottom"
            :hide-triggers="() => ['hover']"
            :triggers="['hover']">
            <div class="cursor-pointer">
              <DynamicIcon
                name="circle-question"
                class="w-4 h-4" />
            </div>
            <template #popper>
              <div class="text-white text-3 leading-4 p-2">
                {{ t("cards.bin-auto") }}
              </div>
            </template>
          </UITooltip>
        </template>
      </div>

      <!--  Recommended for  -->
      <div
        v-if="showRecommended"
        class="cell">
        <ul class="flex items-center space-x-2">
          <li
            v-for="(recommended, i) in item.recommended"
            :key="i">
            <DynamicIcon
              class="w-5 h-5 min-w-[20px]"
              :name="`social-circle-${recommended}`" />
          </li>
        </ul>
      </div>

      <!--  Transaction fee  -->
      <div class="cell justify-end">
        <div class="inline">
          <span
            v-if="item.default?.transferFee"
            class="mr-2 text-neutral-400 line-through">
            {{ Number(item.default.transferFee) }} $
          </span>
          <span>
            <!-- eslint-disable-next-line -->
            {{
              item.blackListed
                ? Number(fakeTariff.fee_transaction_amount)
                : Number(props.selectedTariff.fee_transaction_amount)
            }}
            $
          </span>
        </div>
      </div>

      <!--  Deposit fee  -->
      <CreateCardSelectBinCellFee
        :item="item"
        :selected-tariff="selectedTariff">
        <template
          v-if="props.isExperimentalAutobuy"
          #fee>
          {{ getExperimentalAutobuyPercentByKey(item, "fee_topup") }} %
        </template>
      </CreateCardSelectBinCellFee>

      <!--  Cashback  -->
      <div class="cell justify-end">
        <div class="inline">
          <span> {{ getCashbackPercent(item) }} % </span>
        </div>
      </div>

      <!--  Month Payment  -->
      <div
        v-if="!isMonthlyPaymentColumnInvisible"
        class="cell justify-end">
        <template v-if="!item.blackListedLabel">
          <span
            v-if="item.default?.monthlyPayment"
            class="mr-2 text-neutral-400 line-through">
            {{ Number(item.default.monthlyPayment) }} $
          </span>
          <span>
            <!-- eslint-disable-next-line -->
            {{
              item.blackListed
                ? Number(fakeTariff.card_price)
                : Number(props.selectedTariff.card_price)
            }}
            $
          </span>
        </template>
        <template
          v-if="item.blackListedLabel && item.blackListedLabel.length > 0">
          {{ item.blackListedLabel }}
        </template>
      </div>

      <div class="cell">
        <!--TODO: https://pspace.me/p/pst/issues/4069 -->
        <!--<UIButton-->
        <!--  v-if="-->
        <!--    isActiveSubscriptionOnlyExperiment && availableAtHigherTariff(item)-->
        <!--  "-->
        <!--  class="w-full text-lg"-->
        <!--  color="black"-->
        <!--  size="xs"-->
        <!--  @click="onImproveTariffHandle(item)">-->
        <!--  {{ t("btn.go") }}-->
        <!--</UIButton>-->
        <UIButton
          class="w-full text-lg whitespace-nowrap"
          :color="item.blackListed ? 'black' : 'grey-solid'"
          data-testid="select-bin-table-item-button"
          size="xs"
          @click="onSelectBinHandle(item)">
          {{ getButtonName(item) }}
        </UIButton>
      </div>
    </template>
  </UITableV2>

  <div
    v-else
    class="grid grid-cols-1 sm:grid-cols-2 gap-3 flex-wrap">
    <template
      v-for="(item, i) of props.bins"
      :key="i">
      <BinGridItem
        :bin="item"
        :tariff="item.blackListed ? fakeTariff : props.selectedTariff"
        :subscription="item.private ? true : subscriptionsStatus"
        :cashback="item.blackListed ? fakeCashback : cashbackPercent"
        :subscription-level="subscriptionLevel"
        @select-bin="onSelectBinHandle(item)" />
    </template>
  </div>
  <UIModal
    :is-open="showMemberDialog"
    :with-close-icon="true"
    @close="showMemberDialog = false">
    <template #title>
      <div class="">
        <span class="font-medium text-6 leading-7">
          {{ $t("cards.member-modal.title") }}
        </span>
      </div>
    </template>
    <template #content>
      <div class="flex flex-col">
        <div class="py-4">
          <p class="font-normal text-4.5 leading-6">
            {{ $t("cards.member-modal.text") }}
          </p>
        </div>
        <div>
          <UIButton
            class="w-full"
            color="black"
            @click="showMemberDialog = false">
            OK
          </UIButton>
        </div>
      </div>
    </template>
  </UIModal>
</template>

<style lang="scss" scoped>
.select-bin-table {
  @apply text-3.5;
  .new-bin ~ div,
  .new-bin {
    @apply bg-bg-orange-light;
  }

  :deep(.ui-table) {
    @apply w-full;
  }

  :deep(.ui-table__header) {
    @apply bg-bg-level-2;
  }

  :deep(.ui-table-header-column) {
    @apply py-3 items-end;
    .label {
      @apply leading-4;
    }
  }

  .cell {
    @apply flex items-center space-x-1;
  }

  :deep(.grid-class-7) {
    grid-template-columns:
      120px
      90px
      120px
      minmax(110px, 250px)
      minmax(120px, 250px)
      minmax(110px, 250px)
      160px;
  }
  :deep(.grid-class-8) {
    grid-template-columns:
      120px
      90px
      120px
      minmax(110px, 200px)
      minmax(110px, 200px)
      minmax(120px, 200px)
      minmax(110px, 150px)
      160px;
  }
  :deep(.grid-class-9) {
    grid-template-columns:
      120px
      90px
      110px
      minmax(140px, 156px)
      minmax(110px, 132px)
      minmax(100px, 132px)
      minmax(90px, 132px)
      minmax(110px, 150px)
      160px;
  }
}
</style>
