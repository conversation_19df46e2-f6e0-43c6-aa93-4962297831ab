import { useTracker, TrackerEvent } from "@/composable";
import type { RouteLocation } from "vue-router";

const utmMiddleware = async (to: RouteLocation) => {
  const tracker = useTracker();
  if (to.query.utm_source === "c_io") {
    const statsObj = {
      source: to.query.source,
      medium: to.query.medium,
      campaign: to.query.campaign,
      content: to.query.content,
    };

    await tracker.logEvent(TrackerEvent.CUSTOMER_IO, statsObj);

    const nextQuery = { ...to.query };
    delete nextQuery.utm_source;
    delete nextQuery.source;
    delete nextQuery.medium;
    delete nextQuery.content;
    delete nextQuery.campaign;

    return { path: to.path, query: nextQuery };
  }
};

export default utmMiddleware;
