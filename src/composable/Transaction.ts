import { computed, ref, watchEffect } from "vue";
import { maxBy } from "lodash";
import { getAccountUsdBalance, prepareAccountBalance } from "@/helpers/account";
import { useUserAccounts } from "@/composable/useUserAccounts";
import {
  allCryptoGateway,
  allExternalGateway,
  fiatIso,
} from "@/config/accounts";
import { useEmail, useReason, useWalletAddress } from "@/helpers/validation";
import { useUserStore } from "@/stores/user";
import { useI18n } from "vue-i18n";
import { useStorage } from "@vueuse/core";
import type { TExchangeRates } from "@/types/dictionary/dictionary.types";
import type {
  TDepositGateway,
  TDepositState,
  TDepositToDirection,
  TTransactionBlockedState,
  TTransactionFromDirection,
  TTransactionToDirection,
  TTransferState,
  TWithdrawConfig,
  TWithdrawState,
} from "@/components/Transaction/Latest/types";
import { useLayoutStore } from "@/stores/layout.deprecated";
import type { TAccount } from "@/types/TAccount";
import { useTracker, TrackerEvent } from "./useTracker";

const transferDefaultState: TTransferState = {
  type: "",
  step: 1,
  fromId: -1,
  toId: -1,
  fromAmount: "0",
  toAmount: "0",
  fromDirection: "",
  toDirection: "",
  isSubmit: false,
  isConfirm: false,
  loading: false,
  error: {},
};

const depositDefaultState: TDepositState = {
  step: 1,
  fromId: -1,
  toId: -1,
  fromAmount: "0",
  toAmount: "0",
  toDirection: "",
  isSubmit: false,
  isConfirm: false,
  loading: false,
  error: {},
};

const withdrawDefaultState: TWithdrawState = {
  step: 1,
  fromId: -1,
  fromDirection: "account",
  fromAmount: "0",
  toAmount: "0",
  toDirection: "",
  isSubmit: false,
  isConfirm: false,
  loading: true,
  error: {},
};

/**
 * Manages transfer state
 *
 * @category Composables
 */
export function useTransfer() {
  //setup
  const state = ref<TTransferState>(
    JSON.parse(JSON.stringify(transferDefaultState))
  );
  const layoutStore = useLayoutStore();
  const rates = ref<TExchangeRates | null>(null);

  const setFromDirection = (
    id: number | string,
    direction: TTransactionFromDirection | string
  ) => {
    state.value.fromId = String(id);
    state.value.fromDirection = direction;
  };

  const setToDirection = (
    id: number | string,
    direction: TTransactionToDirection | string
  ) => {
    state.value.toId = String(id);
    state.value.toDirection = direction;
  };

  // for deposit, from direction === 'account'
  const setAccountForFromWithMaxUsd = (accounts: TAccount[]) => {
    const mapAccounts = accounts.map((account: TAccount) => {
      if (account.id !== state.value.toId) {
        return {
          id: account.id,
          balance: Number(getAccountUsdBalance(account, rates.value)),
        };
      }
    });
    const accountWithMaxUsd = maxBy(mapAccounts, "balance");
    if (accountWithMaxUsd) setFromDirection(accountWithMaxUsd.id, "account");
  };

  const setDefaultState = () => {
    state.value = JSON.parse(JSON.stringify(transferDefaultState));
  };

  const replaceQuery = async (route: any, router: any) => {
    const query = Object.assign({}, route.query);
    if (!query.action) {
      layoutStore.state.actions = false;
      query.action = "transaction";
    }
    if (!query.type) {
      query.type = state.value.type;
    }
    if (state.value.fromDirection) {
      query.from_direction = state.value.fromDirection;
    }
    if (state.value.toDirection) {
      query.to_direction = state.value.toDirection;
    }
    if (state.value.fromId !== -1) {
      query.from_id = String(state.value.fromId);
    }
    if (state.value.toId !== -1) {
      query.to_id = String(state.value.toId);
    }
    if (Number(state.value.fromAmount) > 0) {
      query.from_amount = String(state.value.fromAmount);
    }
    if (Number(state.value.toAmount) > 0) {
      query.to_amount = String(state.value.toAmount);
    }
    await router.replace({ query });
  };

  return {
    state,
    rates,
    setFromDirection,
    setToDirection,

    setAccountForFromWithMaxUsd,
    setDefaultState,
    replaceQuery,
  };
}

/**
 * Manages deposit state
 *
 * @category Composables
 */
export function useDeposit() {
  const tracker = useTracker();

  const state = ref<TDepositState>(
    JSON.parse(JSON.stringify(depositDefaultState))
  );
  const layoutStore = useLayoutStore();
  const { accounts } = useUserAccounts();
  const allDepositGateway = computed<TDepositGateway[]>(() => {
    const otherAccounts = accounts.value
      .filter(
        (acc: TAccount) => !fiatIso.includes(String(acc?._currency?.iso_code))
      )
      .map((acc: TAccount) => {
        return {
          id: acc.id,
          title:
            acc?._currency?.iso_code === "USDT"
              ? "Tether USDT (TRC20)"
              : acc?._currency?.name,
          icon:
            acc?._currency?.iso_code === "USDT"
              ? "usdt-trc20"
              : acc?._currency?.iso_code,
          type: "account",
        };
      });
    const cryptoGateway = allCryptoGateway.map(
      (gateway: { title: string; key: string; iso_code: string }) => {
        return {
          id: gateway.key,
          title: gateway.title,
          icon: `square-${gateway.key}`,
          iso_code: gateway.iso_code,
          type: "crypto",
        };
      }
    );
    const externalGateway = allExternalGateway.map(
      (gateway: { title: string; key: string }) => {
        return {
          id: gateway.key,
          title: gateway.title,
          icon: gateway.key,
          type: "external",
        };
      }
    );
    return [...otherAccounts, ...cryptoGateway, ...externalGateway];
  });

  const activeAccount = computed<TAccount | undefined>(() => {
    return accounts.value.find(
      (account: TAccount) => String(account.id) == state.value.toId
    );
  });

  const usdtAccount = computed<TAccount | undefined>(() => {
    return accounts.value.filter(
      (account: TAccount) => account?._currency?.iso_code === "USDT"
    )[0];
  });

  const activeCrypto = computed(() => {
    return allCryptoGateway.find(
      (gateway: { key: string }) => gateway.key === state.value.toId
    );
  });

  const setState = (newState: any) => {
    state.value = Object.assign(state.value, newState);
  };
  const setGateway = (id: number | string, direction: TDepositToDirection) => {
    const selectedDepositMethod = allDepositGateway.value.find(
      (gateway) => gateway.id === id
    );

    if (selectedDepositMethod) {
      tracker.logEvent(TrackerEvent.DEPOSIT_METHOD_SHOW, {
        crypto: selectedDepositMethod.title,
      });
    }

    state.value.toId = String(id);
    state.value.toDirection = direction;
  };

  const replaceQuery = async (route: any, router: any) => {
    const query = Object.assign({}, route.query);
    if (!query.action) {
      layoutStore.state.actions = false;
      query.action = "transaction";
    }
    if (!query.action) {
      layoutStore.state.actions = false;
      query.action = "transaction";
    }
    if (!query.type) {
      query.type = "deposit";
    }
    if (state.value.toDirection) {
      query.to_direction = state.value.toDirection;
    }
    if (state.value.toId) {
      query.to_id = String(state.value.toId);
    }
    await router.replace({ query });
  };

  watchEffect(() => {
    if (
      (state.value.toDirection === "account" &&
        !state.value.toId &&
        usdtAccount.value) ||
      (!state.value.toDirection && !state.value.toId && usdtAccount.value)
    ) {
      setGateway(usdtAccount.value?.id, "account");
    }
    if (state.value.toDirection === "crypto" && !state.value.toId) {
      setGateway(allCryptoGateway[0].key, "crypto");
    }
    if (state.value.toDirection === "external" && !state.value.toId) {
      setGateway(allExternalGateway[0].key, "external");
    }

    state.value.loading =
      !usdtAccount.value ||
      !allDepositGateway.value ||
      (state.value.toDirection === "account" && !activeAccount.value);
  });
  return {
    state,
    allDepositGateway,
    activeAccount,
    activeCrypto,
    usdtAccount,
    setGateway,
    setState,
    replaceQuery,
  };
}

/**
 * Manages withdraw state
 *
 * @category Composables
 */
export function useWithdraw(config: TWithdrawConfig) {
  const state = ref<TWithdrawState>(
    JSON.parse(JSON.stringify(withdrawDefaultState))
  );
  const toEmail = useEmail("email", "");
  const toAddress = useWalletAddress("address", "", "usdt-trc20");
  const reason = useReason("reason", "");
  const rates = ref<any>({});
  const tariff = ref<any>([]);
  const { t } = useI18n();
  const layoutStore = useLayoutStore();
  const userStore = useUserStore();
  const { userKey } = useUserStore();
  const { accounts } = useUserAccounts();

  const blockedState = useStorage<TTransactionBlockedState>(
    `${userKey}-withdraw-user`,
    {
      state: false,
      date: null,
      duration: 0,
    }
  );

  const activeAccount = computed<TAccount | undefined>(() => {
    return accounts.value.find(
      (account: TAccount) => String(account.id) == state.value.fromId
    );
  });

  const usdtAccount = computed<TAccount | undefined>(() => {
    return accounts.value.filter(
      (account: TAccount) => account?._currency?.iso_code === "USDT"
    )[0];
  });

  const accountWithMaxUsd = computed(() => {
    const mapAccounts = accounts.value.map((account: TAccount) => {
      if (account.id !== state.value.fromId) {
        return {
          id: account.id,
          balance: Number(getAccountUsdBalance(account, rates.value)),
        };
      }
    });
    return maxBy(mapAccounts, "balance") || undefined;
  });

  const fromAmountMax = computed<number>(() => {
    return Number(activeAccount.value?.balance);
  });

  const fromAmountMin = computed(() => {
    return Number(userStore.userFees?.min_withdraw_amount ?? 0) <=
      config.minFeeAmount
      ? config.minFeeAmount + 1
      : Number(userStore.userFees?.min_withdraw_amount);
  });

  const externalFee = computed<number>(() => {
    return Number(userStore.userFees?.withdraw || 0);
  });

  const exchangeFee = computed<number>(() => {
    return state.value.toDirection === "external"
      ? Number(state.value.fromAmount) - Number(state.value.toAmount)
      : 0;
  });

  const setState = (newState: any) => {
    state.value = Object.assign(state.value, newState);
    state.value.fromDirection = "account";
    if (newState.toEmail) toEmail.value.value = newState.toEmail;
    if (newState.toAddress) toAddress.value.value = newState.toAddress;
    if (newState.toAmount) {
      changeAmount(newState.toAmount, "toAmount");
    }
  };

  const setDirection = (
    id: TTransactionToDirection | number | string,
    direction: "from" | "to"
  ) => {
    if (direction === "from") state.value.fromId = id;
    else {
      state.value.toDirection = String(id);
      state.value.fromId = -1;
    }
  };
  const convertValue = (amount: number, input: "fromAmount" | "toAmount") => {
    if (input === "fromAmount") {
      if (state.value.toDirection === "external") {
        return amount * externalFee.value > config.minFeeAmount
          ? amount / (1 - Number(externalFee.value))
          : amount + config.minFeeAmount;
      }
      if (state.value.toDirection === "user") {
        return amount;
      }
    } else if (input === "toAmount") {
      if (state.value.toDirection === "external") {
        return amount * externalFee.value > config.minFeeAmount
          ? amount - amount * externalFee.value
          : amount - config.minFeeAmount;
      }
      if (state.value.toDirection === "user") {
        return amount;
      }
    }

    return Number(amount);
  };

  const changeAmount = (
    amount: number,
    input: "fromAmount" | "toAmount",
    isoCode?: string
  ) => {
    if (input === "fromAmount") {
      state.value.fromAmount = prepareAccountBalance(
        amount,
        isoCode || String(activeAccount.value?._currency?.iso_code)
      );
      state.value.toAmount = prepareAccountBalance(
        convertValue(amount, "toAmount"),
        isoCode || String(activeAccount.value?._currency?.iso_code)
      );
    } else {
      state.value.toAmount = prepareAccountBalance(
        amount,
        isoCode || String(activeAccount.value?._currency?.iso_code)
      );
      state.value.fromAmount = prepareAccountBalance(
        convertValue(amount, "fromAmount"),
        isoCode || String(activeAccount.value?._currency?.iso_code)
      );
    }
    validateWithdraw();
  };

  const validateWithdraw = () => {
    const fromValue = Number(state.value.fromAmount);

    // Check max value for from direction
    if (fromValue > Number(activeAccount.value?.balance)) {
      state.value.error.selectFrom = t("errors.notEnoughFunds");
    } else {
      delete state.value.error.selectFrom;
    }

    if (
      state.value.toDirection === "external" &&
      fromValue < fromAmountMin.value
    ) {
      state.value.error.inputFrom = t("errors.minimumAmount", {
        a: fromAmountMin.value,
        c: ` ${activeAccount.value?._currency?.iso_code}`,
      });
      state.value.toAmount = "0";
    } else {
      delete state.value.error.inputFrom;
    }
    return;
  };

  const replaceQuery = async (route: any, router: any, reset?: boolean) => {
    const query = Object.assign({}, route.query);
    if (!query.action) {
      layoutStore.state.actions = false;
      query.action = "transaction";
    }
    if (!query.type) {
      query.type = "withdraw";
    }
    if (reset) {
      delete query.from_id;
      delete query.from_amount;
      delete query.to_direction;
      delete query.to_amount;
      delete query.to_email;
      delete query.to_address;
    } else {
      // if (state.value.fromDirection) {
      //   query.from_direction = String(state.value.fromDirection);
      // }
      if (!state.value.fromId || state.value.fromId !== -1) {
        query.from_id = String(state.value.fromId);
      }
      // if (Number(state.value.fromAmount) > 0) {
      //   query.from_amount = state.value.fromAmount;
      // }
      if (state.value.toDirection) {
        if (userStore.isSuspicious) {
          query.to_direction = "external";
        } else {
          query.to_direction = String(state.value.toDirection);
        }
      }
      if (Number(state.value.toAmount) > 0) {
        query.to_amount = state.value.toAmount;
      }
      if (state.value.toDirection === "user" && toEmail.value.value) {
        query.to_email = String(toEmail.value.value);
      }
      if (state.value.toDirection === "external" && toAddress.value.value) {
        query.to_address = String(toAddress.value.value);
      }
    }
    await router.replace({ query });
  };
  const setDefaultState = () => {
    state.value = JSON.parse(JSON.stringify(withdrawDefaultState));
  };

  watchEffect(() => {
    if (usdtAccount.value?.id && accountWithMaxUsd.value?.id) {
      state.value.loading = false;
    }
    if (Number(state.value.toAmount) > 0) validateWithdraw();
  });

  return {
    state,
    toEmail,
    toAddress,
    accounts,
    activeAccount,
    usdtAccount,
    accountWithMaxUsd,
    fromAmountMax,
    fromAmountMin,
    rates,
    tariff,
    reason,
    externalFee,
    exchangeFee,
    blockedState,
    changeAmount,
    setState,
    setDirection,
    convertValue,
    replaceQuery,
    validateWithdraw,
    setDefaultState,
  };
}
