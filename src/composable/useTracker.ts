import { AmplitudeTrackerService } from "@/libs/amplitude";
import { GoogleTrackerService } from "@/libs/gtag";
import { MatomoTrackerService } from "@/libs/matomo";
import * as Sentry from "@sentry/vue";

export interface ITrackerService {
  logEvent(eventName: string, data?: any): Promise<any>;

  setUserId(id: string): Promise<any>;

  flush(): Promise<any>;

  setUserProperty(property: string, value: string): Promise<any>;
}

export enum TrackerEvent {
  OPEN_PRIVATE_DASHBOARD = "openPrivateDashboard",
  SELECT_ACCOUNT_ = "select_account_",
  DEPOSIT_NOTIFICATION_DISPLAYED = "deposit notification displayed",
  OPEN_TELEGRAM_CHANNEL = "openTelegramChannel",
  OPEN_TWITTER_CHANNEL = "openTwitter",
  PAYMENT_METHOD_SELECTED = "payment method selected",
  CRYPTO_DEPOSIT_PROCESSING = "crypto deposit processing",
  LOGGED_IN = "logged in",
  ACCOUNT_CREATED = "account created",
  CREATE_ACCOUNT_STARTED = "reg_page-opened",
  ACCOUNT_INFO_SUBMITTED = "account info submitted",
  CARD_ISSUED = "card issued",
  CARD_TOP_UP = "card top up",
  EVENT_NAME_ERROR = "event_name_error",
  CLICK_EXTENSION_BTN = "click extension",
  VIEW_DASHBOARD = "dashboard_page-opened",
  BUTTON = "Button",
  VISIT = "Visit",
  PROMOCODE_ACTIVATE = "promocode_activated",
  PROMOCODE_USED = "promocode used",
  CUSTOMER_IO = "customer_io",
  JOIN_PRIVATE_BUTTON_V1 = "join-private-button-v1",
  USER_UPGRADED_SUBSCRIPTION = "user_upgraded_subscription",
  USER_BUY_SUBSCRIPTION = "user_buy_subscription",
  DEPOSIT_SHOW_QR = "deposit show qr",
  DEPOSIT_METHOD_SHOW = "deposit method show",
  DEPOSIT_METHOD_SHOW_QR = "deposit method show qr",
  DEPOSIT_METHOD_CONTINUE = "deposit method continue",
  DEPOSIT_METHOD_AUTOBUY = "deposit method autobuy",
  FINAL_BLOCK_BY_LOCATION = "final_block_by_location",
  OPTIONAL_BLOCK_BY_LOCATION = "optional_block_by_location",
  NEW_REGISTRATION_NEWPST = "new_registration_newpst",
  BUY_CARD_NEWPST = "buy_card_newpst",
  BUY_SUBSCRIPTION_NEWPST = "buy_subscription_newpst",
}

export enum ExceptionReason {
  API_KEY_INVALID = "API_KEY_INVALID",
  NOT_FOUND = "NOT_FOUND",
}

export enum ExceptionCause {
  AMPLITUDE = "AMPLITUDE",
  GOOGLE = "GOOGLE",
  MATOMO = "MATOMO",
}

const trackers = [
  new AmplitudeTrackerService(),
  new GoogleTrackerService(),
  new MatomoTrackerService(),
];

/**
 * Tracker service for user actions, set user properties, and gather analytics data.
 * Abstract tracking in amplitude and google.
 * @category Composables
 */
export function useTracker(): ITrackerService {
  const service: ITrackerService = {
    logEvent: async (eventName, data) => {
      const promises = trackers.map((tracker) =>
        tracker.logEvent(eventName, data).catch((ex) => {
          Sentry.captureException(ex);
          console.error("Tracker.service->logEvent error handled: ", ex);
        })
      );

      return Promise.all(promises);
    },

    setUserProperty: async (property, value) => {
      const promises = trackers.map((tracker) =>
        tracker.setUserProperty(property, value).catch((ex) => {
          Sentry.captureException(ex);
          console.error("Tracker.service->setUserProperty error handled: ", ex);
        })
      );

      return Promise.all(promises);
    },

    setUserId: async (userId) => {
      const promises = trackers.map((tracker) =>
        tracker.setUserId(userId).catch((ex) => {
          Sentry.captureException(ex);
          console.error("Tracker.service->setUserId error handled: ", ex);
        })
      );

      return Promise.all(promises);
    },

    flush: async () => {
      const promises = trackers.map((tracker) =>
        tracker.flush().catch((ex) => {
          Sentry.captureException(ex);
          console.error("Tracker.service->flush error handled: ", ex);
        })
      );

      return Promise.all(promises);
    },
  };

  return service;
}
