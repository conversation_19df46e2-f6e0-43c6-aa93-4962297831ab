import {
  ExceptionCause,
  ExceptionReason,
  TrackerEvent,
  type ITrackerService,
} from "@/composable";

export class GoogleTrackerService implements ITrackerService {
  constructor() {}

  public async logEvent(eventName: string) {
    const finalEventName = eventName
      ? eventName.replace(/ /g, "_")
      : TrackerEvent.EVENT_NAME_ERROR;

    try {
      await window.dataLayer?.push({ event: `ganal_custom_${finalEventName}` });
    } catch (ex) {
      console.error(
        `Google.tracker->logEvent error handled: ` +
          `Cause: ${ExceptionCause.GOOGLE}. ` +
          `Reason: ${ExceptionReason.NOT_FOUND}. ` +
          `Event: ${finalEventName}`,
        ex
      );
    }
  }

  async setUserId(): Promise<void> {
    return Promise.resolve(undefined);
  }

  public async flush(): Promise<void> {}

  public async setUserProperty(): Promise<any> {}
}
