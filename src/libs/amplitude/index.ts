import {
  init,
  setUserId,
  track,
  flush,
  identify,
  Identify,
} from "@amplitude/analytics-browser";
import * as Types from "@amplitude/analytics-types";
import Env from "@/config/env";
import * as Sentry from "@sentry/vue";
import { LoggerSentry } from "@/helpers/logger/Logger.sentry";
import {
  ExceptionCause,
  ExceptionReason,
  type ITrackerService,
} from "@/composable";

const apiKeyEnv = Env.amplitudeApiKey;
const defaultApiConfig: Partial<Types.Config> = {
  serverUrl: "https://api-a.pst.net/api2",
  logLevel: Types.LogLevel.Error,
  loggerProvider: new LoggerSentry(Types.LogLevel.Error),
  flushIntervalMillis: 100,
  flushQueueSize: 1,
};

export class AmplitudeTrackerService implements ITrackerService {
  constructor(apiKey: string = apiKeyEnv, config: any = defaultApiConfig) {
    if (!apiKey) {
      const error =
        `Cause: ${ExceptionCause.AMPLITUDE}. ` +
        `Reason: ${ExceptionReason.API_KEY_INVALID}. ` +
        `Api key: ${apiKey}`;

      Sentry.captureMessage(error);
      console.error("Amplitude.tracker->constructor error handled: ", error);
    } else {
      init(apiKey, undefined, config);
    }
  }

  // log event
  public async logEvent(
    eventName: string,
    data: any
  ): Promise<Types.AmplitudeReturn<Types.Result>> {
    return track(eventName, data);
  }

  // set user id
  public async setUserId(id: string): Promise<void> {
    setUserId(id);
  }

  public async flush(): Promise<void> {
    return flush().promise;
  }

  public async setUserProperty(property: string, value: string): Promise<any> {
    const id = new Identify();
    id.set(property, value);
    return identify(id);
  }
}
