import { TrackerEvent, type ITrackerService } from "@/composable";

export class MatomoTrackerService implements ITrackerService {
  constructor() {
    if (!window._paq) {
      console.error("M analitics was not initialized");
    }
  }

  public async logEvent(eventName: string) {
    if (!window._paq) return;
    if (eventName === TrackerEvent.CARD_ISSUED) {
      window._paq.push(["trackGoal", 1]);
    } else if (eventName === TrackerEvent.ACCOUNT_CREATED) {
      window._paq.push(["trackGoal", 2]);
    } else {
      window._paq.push(["trackEvent", "events", eventName]);
    }
  }

  public async setUserId(id: string) {
    if (!window._paq) return;
    window._paq.push(["setUserId", id]);
  }

  public async flush() {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public async setUserProperty(property: string, value: string): Promise<any> {}
}
