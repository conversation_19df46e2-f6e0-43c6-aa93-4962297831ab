import { useTracker, TrackerEvent } from "@/composable";
import { format } from "date-fns";
import { getDomain } from "@/helpers";
import config from "@/config/env";
import { useUserStore } from "@/stores/user";
import { getPaymentsSummaryFn } from "@/api/transactions/functions";
import { isIos } from "@/helpers/isIos";

/**
 * Opens a URL programmatically with security attributes.
 *
 * @example
 * // Open in new tab
 * openLink('https://example.com', true);
 *
 * @category Helpers
 * @param {string} url - URL to open
 * @param {boolean} external - If true, opens in new tab
 * @returns {void}
 */
export function openLink(url: string, external: boolean) {
  if (!isIos()) {
    Object.assign(document.createElement("a"), {
      target: external ? "_blank" : "_self",
      rel: "noopener noreferrer",
      href: url,
    }).click();
  } else {
    window.location.href = url;
  }
}

/**
 * Opens Telegram channel based on locale.
 * Tracks the channel opening event before navigation.
 *
 * @example
 * // Open RU channel
 * await openTelegramChannel('ru');
 *
 * // Open EN channel
 * await openTelegramChannel('es');
 *
 * @category Helpers
 * @param {string} locale - Locale
 * @returns {Promise<void>} Completes after tracking event is logged
 */
export async function openTelegramChannel(locale: string) {
  const tracker = useTracker();
  await tracker.logEvent(TrackerEvent.OPEN_TELEGRAM_CHANNEL, { locale });
  openLink(
    locale?.includes("ru")
      ? config.telegramChannelRu
      : config.telegramChannelEn,
    true
  );
}

/**
 * Opens the Twitter channel in a new tab.
 * Tracks the channel opening event before navigation.
 *
 * @example
 * await openTwitterChannel('en');
 *
 * @category Helpers
 * @param {string} locale - Locale
 * @returns {Promise<void>} Awaits for tracking event to be logged
 */
export async function openTwitterChannel(locale: string) {
  const tracker = useTracker();
  await tracker.logEvent(TrackerEvent.OPEN_TWITTER_CHANNEL, { locale });
  openLink(config.twitterChannel, true);
}

/**
 * Opens the Chrome Web Store page for the PST extension in a new tab.
 * Tracks the event before navigation.
 *
 * @category Helpers
 * @returns {Promise<void>} Resolves after tracking event is logged
 */
export async function openExtension() {
  const tracker = useTracker();
  await tracker.logEvent(TrackerEvent.CLICK_EXTENSION_BTN);
  openLink(config.chromeExtensionUrl, true);
}

/**
 * Opens the private dashboard with user-specific parameters and spending-based mode.
 * Tracks the dashboard opening event before navigation.
 *
 * @example
 * await openPrivateLanding();
 *
 *
 * @returns {Promise<void>} Resolves after navigation completes
 *
 * @description
 * The function:
 * 1. Retrieves user data (email, language, country).
 * 2. Fetches payment summary for last 7 days.
 * 3. Determines dashboard mode based on total spend:
 *    - 'lite' mode if spend > 1000
 *    - 'default' mode otherwise
 * 4. Constructs URL with user context parameters
 *
 * @category Helpers
 * @throws {Error} Logs warning if payment summary fetch fails
 *
 * @example URL format
 * ```
 * ${domain}/private-dashboard?user=<EMAIL>&country=US&lang=en-US&mode=lite
 * ```
 *
 */
export async function openPrivateLanding() {
  const { user, language, userCountry } = useUserStore();
  const tracker = useTracker();

  await tracker.logEvent(TrackerEvent.OPEN_PRIVATE_DASHBOARD, {
    language,
    user: user.uuid,
  });

  try {
    const date = new Date();
    const weekAgo = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate() - 7
    );

    const response = await getPaymentsSummaryFn({
      params: {
        start: format(weekAgo, "yyyy-MM-dd") + " 00:00:00",
        stop: format(date, "yyyy-MM-dd") + " 23:59:59",
      },
    });
    if (response.data) {
      const summary = response.data.summary;
      const totalSpend = summary.approved_sum + summary.pending_sum;

      const urlQuery = {
        user: user.email || "",
        country: userCountry || "",
        lang: language || "en",
        mode: totalSpend > 1000 ? "lite" : "default",
      };
      const url = `${getDomain()}/private-dashboard?${new URLSearchParams(
        urlQuery
      ).toString()}`;
      openLink(url, false);
    }
  } catch (ex) {
    console.warn("Events->openPrivateLanding error handled: ", ex);
  }
}
