import { useAxios } from "@/helpers/axios";
import { useAuth } from "@/composable/Auth";
import { useUserStore } from "@/stores/user";
import { UserService } from "@modules/services/user";

import { useCookies } from "vue3-cookies";
import { OauthService } from "@modules/services/oauth";
import { getHash } from "@/helpers/url";
import { useExperiments } from "@/composable/useExperiments";
import { usePstFetch, useTracker, TrackerEvent } from "@/composable";
import type { TGoogleJwtResponse } from "@/types/TGoogleJwtResponse";
import { LocalStorageKey } from "@/constants/local_storage_key";
import { useSessionStorage } from "@vueuse/core";
import { SessionStorageKey } from "@/constants/session_storage_key";
import { useUserEvents } from "@/composable/useUserEvents";

type TLoginData = {
  email: string;
  password: string;
  token?: string;
  recaptcha_token: string;
};

type TAuthWithGoogleJwtReturn = {
  canAuth: boolean;
  authId: string | null;
};

type TRegisterData = {
  email: string;
  skype?: string;
  telegram?: string;
  type: number;
  password: string;
  country_id: number;
  referral_hash: string;
};

/**
 * Handles authentication-related operations.
 * @TODO this is not a helper. This is not how our Auth should be done. refactor to use composables
 *
 * @category Helpers
 */
class Auth {
  static async loginWithTelegram(params: any) {
    const { data, email } = params;
    const { loading, getUser } = useUserStore();

    const reqParams = {
      params: data,
      email: email,
      referral_hash: params?.hash,
    };

    if (params.master_hash) {
      // @ts-expect-error
      reqParams.master_hash = params.master_hash;
    }

    const result = await useAxios().post("/oauth/telegram/callback", reqParams);

    if (result?.data?.data?.token) {
      const { setToken } = useAuth();
      setToken("Bearer " + result.data.data.token);
    }

    if (!loading) {
      await getUser({
        params: {
          ...getUtm(),
        },
      });
    }

    return result;
  }

  static async loginWithWhatsapp(params: any) {
    const { data, email } = params;
    const { loading, getUser } = useUserStore();

    const reqParams = {
      params: data,
      email: email,
      referral_hash: params?.hash,
    };

    if (params.master_hash) {
      // @ts-expect-error
      reqParams.master_hash = params?.master_hash;
    }

    const result = await useAxios().post("/oauth/whatsapp/callback", reqParams);

    if (result?.data?.data?.token) {
      const { setToken } = useAuth();
      setToken("Bearer " + result.data.data.token);
    }

    if (!loading) {
      await getUser({
        params: {
          ...getUtm(),
        },
      });
    }

    return result;
  }

  static async loginWithGoogle(
    data: string,
    hash: string | undefined,
    master_hash?: string
  ) {
    const params = {
      code: data,
      referral_hash: hash,
    };

    if (master_hash) {
      // @ts-expect-error
      params.master_hash = master_hash;
    }

    const result = await useAxios().post("/oauth/google/callback", params);
    const { loading, getUser } = useUserStore();

    if (result?.data?.data?.token) {
      const { setToken } = useAuth();
      setToken("Bearer " + result.data.data.token);
      await getUser({
        params: {
          ...getUtm(),
        },
      });
    }

    return result;
  }

  static async loginWithApple(
    data: string,
    hash: string | undefined,
    master_hash?: string
  ) {
    const params = {
      code: data.replace(" ", ""),
      referral_hash: hash,
    };
    if (master_hash) {
      // @ts-expect-error
      params.master_hash = master_hash;
    }
    const result = await useAxios().post("/oauth/apple/callback", params);
    const { loading, getUser } = useUserStore();

    if (result?.data?.data?.token) {
      const { setToken } = useAuth();
      setToken("Bearer " + result.data.data.token);
      await getUser({
        params: {
          ...getUtm(),
        },
      });
    }

    return result;
  }

  static async login(data: TLoginData, getUserData: boolean = true) {
    const result: any = await UserService.login(data);

    if (!result.status) {
      return result;
    }

    const { loading, getUser } = useUserStore();

    if (result.data?.token) {
      const { setToken } = useAuth();
      setToken("Bearer " + result.data.token);
      localStorage.removeItem(LocalStorageKey.FIRST_LOGIN);
    }

    if (!loading && getUserData) {
      await getUser();
    }

    return result;
  }

  static async register(data: TRegisterData) {
    const result = await UserService.registration(data);

    if (result.data?.data?.token) {
      const { setToken } = useAuth();
      setToken("Bearer " + result.data.data.token);
      localStorage.setItem(LocalStorageKey.FIRST_LOGIN, "true");
    }

    return result;
  }

  static async logout() {
    const response = await UserService.logout();

    if (!response.status) {
      return false;
    }

    const { resetExperimentsValues } = useExperiments();
    const { delToken } = useAuth();
    const { stopTrackingEvents } = useUserEvents();

    resetExperimentsValues();
    delToken();
    stopTrackingEvents();

    localStorage.removeItem(LocalStorageKey.EMAIL_VERIFICATION_TOOLTIP);

    return true;
  }
}

/**
 * Retrieves UTM parameters from cookies and formats them.
 * Removes '[]' from parameter keys if present.
 *
 * @example
 * // When cookies contain:
 * // utm = { 'source[]': 'google', 'medium[]': 'cpc', 'campaign[]': 'spring_sale' }
 * getUtm()
 * // Returns:
 * // {
 * //   source: 'google',
 * //   medium: 'cpc',
 * //   campaign: 'spring_sale'
 * // }
 *
 * // When no UTM parameters are present
 * getUtm()
 * // Returns: {}
 *
 * @category Helpers
 * @returns {Object} An object containing cleaned UTM parameters
 */
export function getUtm() {
  const cookies = useCookies();

  const utm = cookies.cookies.get("utm") || {};

  return Object.entries(utm).reduce((accum: any, item: [string, any]) => {
    const [key, value] = item;
    const finalKey = key.replace("[]", "");

    accum[finalKey] = value;

    return accum;
  }, {});
}

/**
 * Authenticates user with Google JWT token and handles post-authentication flow.
 * @TODO should not be in helpers, move
 *
 * @category Helpers
 * @param {TGoogleJwtResponse} data - Google JWT response
 * @returns {Promise<TAuthWithGoogleJwtReturn>}
 * canAuth: true if authentication successful, false otherwise.
 * authId: takes id for passing 2 factor auth further flow, can be null if authentication is successful.
 */
export async function authWithGoogleJwt(
  data: TGoogleJwtResponse
): Promise<TAuthWithGoogleJwtReturn> {
  const authLogic = useAuth();
  const userStore = useUserStore();
  const tracker = useTracker();

  const result = await OauthService.jwtCallback({
    id_token: data.credential,
    referral_hash: getHash() || undefined,
  });

  if (!result.status || !result.data?.data?.token) {
    return { canAuth: false, authId: result.data?.data?.auth_id || null };
  }

  authLogic.setToken("Bearer " + result.data.data.token);
  await userStore.getUser();

  if (result.data?.data?.type !== "login") {
    await tracker.logEvent(TrackerEvent.ACCOUNT_INFO_SUBMITTED, {
      "account type": "google",
    });

    await tracker.setUserId(userStore.user.uuid!);
    await tracker.logEvent(TrackerEvent.ACCOUNT_CREATED, {
      "account type": "google",
    });
  }

  return { canAuth: true, authId: null };
}

/**
 * Initializes Google Sign-In by loading the Google Identity Services SDK and configuring the client.
 * @TODO should not be in helpers, move
 *
 * @category Helpers
 * @param {string} client_id - Google OAuth client ID
 * @param {function} callback - Callback function to handle Google Sign-In response
 * @returns {void}
 * @throws {Error} Logs error to console if Google window initialization fails
 */
export function initGoogle(
  client_id: string,
  callback: (data: TGoogleJwtResponse) => Promise<void>
) {
  const masterHash = useSessionStorage(SessionStorageKey.MASTER_HASH, "");

  if (masterHash.value) {
    return;
  }

  const element = document.createElement("script");
  element.src = "https://accounts.google.com/gsi/client";
  element.type = "text/javascript";
  element.async = true;
  element.onload = () => {
    try {
      window.google.accounts.id.initialize({
        client_id,
        callback: callback,
      });
      window.google.accounts.id.prompt();
    } catch (e) {
      console.error("cant show google window: ", e);
    }
  };
  document.head.appendChild(element);
}

/**
 * Verifies a two-factor authentication code for OAuth authentication.
 * Used during the 2FA verification process after initial OAuth login.
 * @TODO should not be in helpers, move
 *
 * @category Helpers
 * @param {Object} params - Verification parameters
 * @param {string} params.authId - session ID
 * @param {string} params.token - two-factor authentication code
 * @returns {Promise<any>} Response containing token if verification successful
 * @throws {Error} When verification fails or server returns non-200 status
 */
export async function oauthVerify2fa({
  authId,
  token,
}: {
  authId: string;
  token: string;
}) {
  const { data, statusCode } = await usePstFetch("oauth/verify-2fa")
    .post({
      auth_id: authId,
      token,
    })
    .json();
  if (statusCode.value !== 200) {
    throw new Error();
  }
  return data;
}

/**
 * Resends a two-factor authentication code for OAuth authentication.
 * @TODO should not be in helpers, move
 *
 * @category Helpers
 * @param {Object} params - Resend parameters
 * @param {string} params.authId - Current authentication session ID
 * @returns {Promise<{ auth_id: string }>} Response containing new auth_id
 * @throws {Error} When resend fails or server returns non-200 status
 */
export async function oauthResend2fa({ authId }: { authId: string }) {
  const { data, statusCode } = await usePstFetch("oauth/resend-2fa")
    .post({
      auth_id: authId,
    })
    .json();
  if (statusCode.value !== 200) {
    throw new Error();
  }
  return data;
}

// TODO we do not do default exports
export default Auth;
