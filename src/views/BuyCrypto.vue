<script setup lang="ts">
import ModalScreen from "@/components/ui/Modal/ModalScreen.vue";
import { useUserStore } from "@/stores/user";
import { useAccountsLogic } from "@/composable/AccountsLogic/AccountsComposable";
import { computed, onMounted } from "vue";
import Env from "@/config/env";
import UiDivider from "@/components/ui/Divider/Divider.vue";
import { SHA512, enc } from "crypto-js";
import { v4 as uuid } from "uuid";
import { useTracker, TrackerEvent } from "@/composable";
const { isTeamOwner } = useUserStore();

const close = () => {
  window.location.assign(isTeamOwner ? "/team" : "/app");
};

const accountsLogic = useAccountsLogic();
const tracker = useTracker();
const widgetId = Env.mercuryoWidgetId;

const usdtAccount = computed(() => {
  return accountsLogic.list.value.find((a: any) => a.currency_id === 15);
});

onMounted(() => {
  accountsLogic.updateAccounts().then(() => {
    let script = document.createElement("script");
    script.setAttribute("src", "https://widget.mercuryo.io/embed.2.0.js");

    script.onload = () => {
      if (!usdtAccount.value) {
        return;
      }

      const acc: any = usdtAccount.value;
      const address = (acc?.addresses[0]?.address || "").toString();
      const secret = SHA512(address + "xw12").toString(enc.Hex);
      const settings = {
        widgetId: widgetId,
        host: document.getElementById("mercuryo-widget"),
        type: "buy",
        currency: "USDT",
        network: "TRON",
        fixCurrency: true,
        ratesFeeOff: false,
        address: address,
        returnUrl: window.location.origin + "/app",
        merchantTransactionId: uuid().toString(),
        signature: secret,
        width: "100%",
        height: "100%",
        redirectUrl: window.location.origin + "/app",
        onStatusChange: function (data: any) {
          tracker.logEvent(TrackerEvent.CRYPTO_DEPOSIT_PROCESSING, data);
        },
      };

      // @ts-ignore
      mercuryoWidget.run(settings);
    };

    document.head.appendChild(script);
  });
});
</script>

<template>
  <ModalScreen
    boxed
    centered
    @close="close">
    <template #title>
      {{ $t("Deposit") }}
    </template>

    {{ $t("deposit.visa.infoText") }}

    <ui-divider class="mt-5 mb-5" />

    <div v-if="usdtAccount">
      <div id="mercuryo-widget"></div>
    </div>
  </ModalScreen>
</template>

<style>
#mercuryo-widget {
  height: 100vh;
}
</style>
