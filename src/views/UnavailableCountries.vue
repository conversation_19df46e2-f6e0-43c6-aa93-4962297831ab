<script setup lang="ts">
import UiTransition from "@/components/ui/UITransition.vue";
import { ref } from "vue";
import { useUserStore } from "@/stores/user";
import BlockedCountriesModal from "@/components/Auth/BlockedCountriesModal.vue";
import { useRouter } from "vue-router";
import { useCountries } from "@/composable/useCountries";
import { useTracker, TrackerEvent } from "@/composable";
import { ERemoteStorageKeys } from "@modules/services/remoteStorage";
import { RouteName } from "@/constants/route_name";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { useFetch, useSessionStorage } from "@vueuse/core";
import { SessionStorageKey } from "@/constants/session_storage_key";
import SupportTrigger from "@/components/SupportTrigger.vue";
import { useCheckGet } from "@/composable/API/useCheckGet";

const { countryIsAvailable } = useCountries();
const { getUserCountryCode } = useUserStore();
const router = useRouter();
const wasGeoBlockShown = useSessionStorage(
  SessionStorageKey.WAS_GEO_BLOCK_SHOWN,
  false
);
const tracker = useTracker();

const isShowModal = ref<boolean>(false);
const isLoaded = ref<boolean>(false);
const countryCode = ref("");
const isTotallyRestrictedCountry = ref(false);

const openFullListUnavailableCountriesModal = () => {
  isShowModal.value = true;
};

const checkCountryCode = async () => {
  const { data: checkData } = await useCheckGet();
  if (
    checkData.value?.data.score &&
    [2, 3].includes(checkData.value?.data.score)
  ) {
    isTotallyRestrictedCountry.value = true;
    countryCode.value = await getUserCountryCode();
    if (!countryCode.value.length) {
      const { data } = await useFetch("https://api.pst.net/info").json();
      countryCode.value = data.value?.data.country_code;
    }

    await tracker.logEvent(ERemoteStorageKeys.SHOULD_GEO_BLOCK, {});
    tracker.logEvent(TrackerEvent.FINAL_BLOCK_BY_LOCATION);
    isLoaded.value = true;
    return;
  }
  if (wasGeoBlockShown.value) {
    await router.replace({ name: RouteName.DASHBOARD });
    return;
  }
  countryCode.value = await getUserCountryCode();
  if (!countryCode.value) {
    await router.replace({ name: RouteName.LOGIN });
  }
  if (countryIsAvailable(countryCode.value)) {
    await router.replace({ name: RouteName.DASHBOARD });
  } else {
    await tracker.logEvent(ERemoteStorageKeys.SHOULD_GEO_BLOCK, {});
    tracker.logEvent(TrackerEvent.OPTIONAL_BLOCK_BY_LOCATION);
    isLoaded.value = true;
  }
};

const clickContinue = async () => {
  wasGeoBlockShown.value = true;
  await router.push({ name: RouteName.DASHBOARD });
};

checkCountryCode();
</script>

<template>
  <template v-if="isShowModal">
    <UiTransition :name="'fade'">
      <BlockedCountriesModal
        @close="
          () => {
            isShowModal = false;
          }
        " />
    </UiTransition>
  </template>

  <template v-if="isLoaded">
    <UiTransition
      name="fade-slide-up"
      appear>
      <div class="wrapper flex flex-col flex-auto m-auto w-full">
        <div class="flex flex-auto"></div>
        <div
          class="flex flex-none flex-col items-center text-center font-hauss w-full max-w-[27.5rem] m-auto gap-7 p-2">
          <!-- <div
            v-if="isTotallyRestrictedCountry"
            class="flex flex-none">
            <img
              :src="'/img/geoblock/map.png'"
              alt="map" />
          </div> -->
          <div class="flex flex-none w-full px-10">
            <span
              v-if="isTotallyRestrictedCountry && countryCode === 'RU'"
              class="text-fg-primary text-center text-6 not-italic font-semibold leading-7">
              Сервис недоступен для пользователей из Российская Федерация
            </span>
            <span
              v-else
              class="text-fg-primary text-center text-6 not-italic font-semibold leading-7">
              {{
                $t("unavailable-country.title", {
                  c: $t(`unavailable-country-list.${countryCode}`),
                })
              }}
            </span>
          </div>
          <template v-if="!isTotallyRestrictedCountry">
            <div class="flex flex-none w-full">
              {{
                $t("unavailable-country.text", {
                  c: $t(`unavailable-country-list.${countryCode}`),
                })
              }}
            </div>
            <div class="flex flex-none w-full">
              <UIButton
                color="black"
                class="w-full"
                @click="clickContinue()">
                <span
                  class="text-fg-contrast text-center text-base not-italic font-medium leading-5">
                  {{ $t("unavailable-country.button.submit") }}
                </span>
              </UIButton>
            </div>
            <div class="flex flex-none">
              <span
                class="text-4 text-fg-secondary leading-5 text-center cursor-pointer"
                @click="openFullListUnavailableCountriesModal()">
                {{ $t("unavailable-country.button.list") }}
              </span>
            </div>
          </template>
        </div>
        <div class="flex flex-auto"></div>
      </div>
    </UiTransition>

    <div
      v-if="!isTotallyRestrictedCountry"
      class="support-wrapper">
      <SupportTrigger color="blue-light" />
    </div>
  </template>
</template>
<style lang="scss" scoped>
.support-wrapper {
  @apply fixed right-4 top-5 z-2;
}

.wrapper {
  @apply h-screen;
}
</style>
