<script lang="ts" setup>
import SocialButtons from "@/components/Auth/SocialButtons.vue";
import ChangeAuthMethod from "@/components/Auth/ChangeAuthMethod.vue";
import ChangeAuthMethodWithError from "@/components/Auth/ChangeAuthMethodWithError.vue";
import SendLinkModal from "@/components/Auth/SendLinkModal.vue";
import SelectLanguage from "@/components/ui/Select/SelectLanguage.vue";
import UiButton from "@/components/ui/Button/Button.vue";
import UiButtonSocial from "@/components/ui/Button/ButtonSocial.vue";
import InputText from "@/components/ui/Input/InputText.vue";
import InputPassword from "@/components/ui/Input/InputPassword.vue";
import BlockedUserModal from "@/components/BlockedUserModal/BlockedUserModal.vue";

import Config from "@/config/env";
import Auth, { authWithGoogleJwt, initGoogle } from "@/helpers/auth";
import type { TAuthMethod } from "@/components/Auth/types";

import { setRefHash } from "@/helpers/url";
import { useRoute, useRouter } from "vue-router";
import { useEmail, usePassword } from "@/helpers/validation";
import { computed, onMounted, ref, watch } from "vue";
import { useTracker, TrackerEvent } from "@/composable";
import { loggerConsole } from "@/helpers/logger/Logger";
import { useUserStore } from "@/stores/user";
import { useDictionary } from "@/stores/dictionary";
import { useI18n } from "vue-i18n";
import { useState } from "@/helpers/utilities";
import { useAuth } from "@/composable/Auth";
import { useAppleLink, useGoogleLink } from "@/composable/Oauth";
import { addCaptcha, delCaptcha } from "@/helpers/captcha";
import { LocalStorageKey } from "@/constants/local_storage_key";
import type { TGoogleJwtResponse } from "@/types/TGoogleJwtResponse";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import { locales } from "@/libs/i18n";
import { useTwoFactorAuthRedirect } from "@/composable/useTwoFactorAuthRedirect";

const { setLanguageLazy } = useI18nWrapper();
const i18n = useI18n();
const { redirectTo2FA } = useTwoFactorAuthRedirect();
const dictionaryStore = useDictionary();
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const logger = loggerConsole();
const password = usePassword();
const email = useEmail();
const authLogic = useAuth();
const googleLink = useGoogleLink();
const appleLink = useAppleLink();
const tracker = useTracker();
const [loading, setLoading] = useState<boolean>(false);
const [capthaLoading, setcapthaLoading] = useState<boolean>(true);
const [authMethod, setAuthMethod] = useState<TAuthMethod>("");
const [errorMessage, setErrorMessage] = useState<string>("");
const [showRecoveryModal, setShowRecoveryModal] = useState<boolean>(false);

const isBlockedUserModalShowed = ref<boolean>(false);
const blockedReason = ref<string>("");
const blockedIframeLink = ref<string>("");

const buttonDisabled = computed(
  () =>
    !(password.value.value && email.value.value) ||
    loading.value ||
    !!password.errorMessage?.value ||
    !!email.errorMessage?.value
);

const callbackPath = computed(() => {
  return route.query?.callback;
});

const redirectToApp = async () => {
  if (callbackPath.value) {
    await router.push(callbackPath.value as string);
  } else {
    await router.push("/app");
  }
};

const setup = async () => {
  if (authLogic.getToken()) {
    await redirectToApp();
  }
};
const signInWithGoogleJwt = async (data: TGoogleJwtResponse) => {
  setLoading(true);

  const { canAuth, authId } = await authWithGoogleJwt(data);

  if (canAuth) {
    await redirectToApp();
  } else if (authId) {
    await redirectTo2FA(authId);
  }

  setLoading(false);
};
const signInWithGoogle = () => {
  setRefHash();
  window.location.href = googleLink.value.value;
};
const signInWithApple = () => {
  setRefHash();
  window.location.href = appleLink.value.value;
};
const signInWithTelegram = () => {
  setRefHash();
  window.location.href =
    // "https://t.me/" + (dictionaryStore.dictionary?.oauth?.bot_name || "");
    "https://t.me/" +
    (dictionaryStore.dictionary?.oauth?.bot_name || "") +
    "?start=login";
  // "?getloginlink=login";
};

const signInWithWhatsapp = async () => {
  await setRefHash();
  window.location.href =
    "https://wa.me/" +
    (dictionaryStore.dictionary?.oauth?.whatsapp_bot || "") +
    "?text=/start";
};

const signInWithEmail = async () => {
  setLoading(true);

  tracker.logEvent(TrackerEvent.LOGGED_IN);
  window.grecaptcha.ready(async function () {
    const recaptcha_token = await window.grecaptcha.execute(Config.grecaptcha, {
      action: "submit",
    });

    const credentials = {
      email: email.value.value.toLowerCase(),
      password: password.value.value,
      token: "",
      recaptcha_token,
    };

    const result = await Auth.login(credentials);

    // Blocked user case
    if (
      result.data?.message === "User not found or wrong password" &&
      result.data?.reason
    ) {
      blockedReason.value = result.data?.reason;
      // If no iframe URL - blocked user is Member
      blockedIframeLink.value = result.data?.link ?? "";
      isBlockedUserModalShowed.value = true;
      setLoading(false);
      return;
    }

    // delete when PST-T-3389 will be ready
    if (result.data?.message === "Token was sent to Telegram") {
      await router.push({
        path: "/confirm-telegram",
        query: {
          identifier: email.value.value,
          password: password.value.value,
        },
      });
      return;
    }

    if (result.data?.message === "Token was sent to your messenger") {
      await router.push({
        path: "/confirm-messenger",
        query: {
          identifier: email.value.value,
          password: password.value.value,
        },
      });
      return;
    }

    if (!result.status) {
      setErrorMessage(result.message || "Something went wrong");
      setLoading(false);
      return;
    }

    if (locales.find((v) => v.code === userStore.language)) {
      i18n.locale.value = userStore.language;
    }

    delCaptcha();
    await redirectToApp();

    setLoading(false);

    localStorage.removeItem(LocalStorageKey.AUTH_METHOD);
    localStorage.removeItem(LocalStorageKey.AUTH_EMAIL);
  });
};

const setLanguageFromLocalStorage = async () => {
  const language = localStorage.getItem(LocalStorageKey.LANGUAGE);
  if (language) {
    await setLanguageLazy(language);
    i18n.locale.value = language;
  }
};

// watch
watch(
  () => authMethod.value,
  async (method: TAuthMethod) => {
    localStorage.setItem(LocalStorageKey.AUTH_METHOD, method);
    if (method === "telegram") {
      await signInWithTelegram();

      setAuthMethod("");
    }
    if (method === "google") {
      await signInWithGoogle();

      setAuthMethod("");
    }
    if (method === "apple") {
      await signInWithApple();

      setAuthMethod("");
    }
    if (method === "whatsapp") {
      await signInWithWhatsapp();
      setAuthMethod("");
    }
  }
);
watch(
  () => email.value.value,
  (newEmail) => {
    localStorage.setItem(LocalStorageKey.AUTH_EMAIL, newEmail);
  }
);

watch(i18n.locale, (value) => {
  email.validate();
  password.validate();
  localStorage.setItem(LocalStorageKey.LANGUAGE, value);
});

setup();

onMounted(() => {
  setLanguageFromLocalStorage();
  addCaptcha();
  const t = setInterval(() => {
    if (!window.grecaptcha) {
      setcapthaLoading(true);
      return;
    }
    setcapthaLoading(false);
    clearInterval(t);
  }, 100);
  appleLink.updateValue();
  googleLink
    .updateValue()
    .then(() => initGoogle(googleLink.clientId.value, signInWithGoogleJwt));

  const authMethod = localStorage.getItem(LocalStorageKey.AUTH_METHOD);
  if (authMethod) {
    setAuthMethod(authMethod as TAuthMethod);
  }
  const authEmail = localStorage.getItem(LocalStorageKey.AUTH_EMAIL);
  if (authEmail) {
    email.value.value = authEmail;
  }
});
</script>

<template>
  <div :class="$style.root">
    <div class="flex flex-col gap-5">
      <!--   Title & text   -->
      <div>
        <h3 class="text-gray-900 text-left">
          {{ $t("signIn.welcomeBack") }}
        </h3>

        <span class="text-neutral-600 text-base">
          {{ $t("signIn.options") }}
        </span>
      </div>

      <!--   Buttons   -->
      <SocialButtons
        :disabled="
          !googleLink.value.value ||
          loading ||
          !appleLink.value.value ||
          capthaLoading
        "
        @click="setAuthMethod" />
    </div>

    <!--  Or  -->
    <div class="flex items-center gap-3">
      <div class="w-full border-dashed border-t border-t-[#838689]" />

      <span class="text-sm text-neutral-600">
        {{ $t("settings.security.divider") }}
      </span>

      <div class="w-full border-dashed border-t border-t-[#838689]" />
    </div>

    <transition
      mode="out-in"
      name="fade-slide-up">
      <!--  Sign in with email  -->
      <div
        v-if="authMethod !== 'email'"
        class="flex flex-col gap-4">
        <UiButtonSocial
          :disabled="loading || capthaLoading"
          :title="$t('signIn.continueWith', { v: $t('social.email') })"
          data-testid="continue-with-email"
          @click="() => setAuthMethod('email')" />

        <!--   Change method   -->
        <ChangeAuthMethod method="signUp" />
      </div>

      <!--  Sign in  -->
      <form
        v-else
        class="flex flex-col gap-8"
        @submit.prevent="signInWithEmail">
        <div class="flex flex-col gap-5">
          <InputText
            :error="email.errorMessage.value"
            :label="$t('email')"
            :value="email.value.value"
            autofocus
            data-cy="login_input_identifier"
            type="email"
            @focus="() => setErrorMessage('')"
            @input="email.setValue" />
          <InputPassword
            :error="password.errorMessage.value"
            :value="password.value.value"
            data-cy="login_input_password"
            @focus="() => setErrorMessage('')"
            @input="password.setValue" />

          <button
            type="button"
            class="self-end"
            data-testid="forgot-password-button"
            @click="() => setShowRecoveryModal(true)">
            {{ $t("signIn.forgotPassword") }}
          </button>
        </div>

        <ChangeAuthMethodWithError
          v-if="!!errorMessage"
          :text="errorMessage"
          data-cy="error-msg" />

        <div class="flex flex-col gap-4">
          <UiButton
            :class="$style.button"
            :disabled="buttonDisabled"
            :title="$t('signIn')"
            data-cy="login_button_login_with_email" />

          <!--   Change method   -->
          <ChangeAuthMethod method="signUp" />
        </div>
      </form>
    </transition>

    <div class="mt-auto flex flex-col gap-5">
      <SelectLanguage />
    </div>

    <Portal to="modals">
      <transition name="modal">
        <SendLinkModal
          v-if="showRecoveryModal"
          @close="() => setShowRecoveryModal(false)" />
      </transition>
    </Portal>

    <Portal to="modals">
      <BlockedUserModal
        :is-open="isBlockedUserModalShowed"
        :restriction-reason="blockedReason"
        :iframe-url="blockedIframeLink"
        @close="isBlockedUserModalShowed = false" />
    </Portal>
  </div>
</template>

<style lang="scss" module>
.root {
  @apply flex flex-col gap-8 w-full h-full;

  & * {
    @apply font-granate;
  }
}

.button {
  @apply font-normal;
}
</style>
