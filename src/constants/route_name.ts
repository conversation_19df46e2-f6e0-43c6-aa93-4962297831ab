export enum RouteName {
  LOGIN = "login",
  AUTH = "auth",
  NOT_FOUND = "404",
  CONFIRM_TELEGRAM = "confirm-telegram",
  OAUTH2 = "oauth2",
  RESET_PASSWORD = "reset-password",
  REGISTER = "register",
  DASHBOARD = "dashboard",
  SUBSCRIPTION_PROMO = "cashback.promo",
  UNAVAILABLE_COUNTRIES = "unavailableCountries",
  CARDS = "cards",
  PERSONAL_PAYMENTS = "personal-payments",
  FINANCE = "finance",
  FAQ = "FAQ",
  FAQ_CATEGORY = "FAQ.category",
  AFFILIATE = "affiliate",
  REWARDS = "rewards",
  ACCOUNTS = "accounts",
  SETTINGS = "settings",
  ACCOUNT_SETTINGS = "settings.account",
  NOTIFICATIONS_SETTINGS = "notifications",
  SESSIONS_SETTINGS = "sessions",
  API_SETTINGS = "api",
  CREATE_CARD = "create-card",
  CREATE_CARD_WITH_SLUG = "create-card-with-slug",
  EXPERIMENTS = "experiments",
  SUBSCRIPTION = "subscription",
  SUBSCRIPTION_DETAIL = "subscription.detail",
  CASHBACK = "subscription.cashback",
  SUBSCRIPTION_TARIFF = "subscription.tariffs",
  // MISSED_CASHBACK = "cashback.missed",
  PROMO_CARDS = "promo-cards",
  PROMO_CARDS_CREATE_OLD = "promo-cards-create-old",
  BUY_CRYPTO = "buy_crypto",
  CARD_DEPOSIT = "card_deposit",
  VERIFICATION_SETTINGS = "kyc_verification",
  OPERATIONS_BY_CARD = "operations-by-card",
  OPERATIONS_BY_ACCOUNT = "operations-by-account",
  TEAM = "personal.route-team",
  TEAM_DASHBOARD = "team.route-dashboard",
  TEAM_OPERATIONS_BY_ACCOUNT = "team.operations-by-account",
  TEAM_OPERATIONS_BY_CARD = "team.operations-by-card",
  TEAM_CARDS = "team.route-cards",
  TEAM_REQUESTS = "team.route-requests",
  TEAM_ACCOUNTS = "team.route-accounts",
  TEAM_FINANCE = "team.route-transactions",
  TEAM_STATISTIC = "statistic",
  TEAM_PAYMENTS = "team.payments",
  TEAM_MEMBERS = "team.route-members",
  TEAM_MEMBER_BY_ID = "team.route-member-by-id",
  CONFIRM_MESSENGER = "confirm-messenger",
  CONFIRM_2FA_SOCIAL = "confirm-2fa-social",
  FEEDBACK_BONUS = "feedback-bonus",
  PAY = "pay",
  PAY_DEMO = "pay-demo",
  EMAIL_VERIFICATION_PROCESS = "email-verification-process",
  EMAIL_VERIFICATION_COMPLETE = "email-verification-complete",
  SELECT_TARIFF = "select-tariff",
}
