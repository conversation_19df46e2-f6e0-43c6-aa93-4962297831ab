export enum LocalStorageKey {
  SNACKBAR = "hidden-snackbars",
  API_ACCESS_WAS_SENT = "api-access-was-sent",
  REF_HASH = "ref_hash",
  SELECTED_CURRENCY_ISO = "ui_dashboard_selectedCurrencyIsoCode", // do we ever read this one?
  LAST_DIGITS_ISSUE_CARD = "lastFourDigitsIssueCardNumber", // key is too long
  CARDS_TABLE_VIEW = "cardsTableView",
  ONE_SIGNAL_CAN_SHOW_BTN_SUBSCRIBE_TS = "oneSignalCanShowBtnSubscribeTs",
  LAST_EXPORT_TIME = "lastExportTimeKey-",
  FORM_PRIVATE = "form_private_plans",
  PAYMENTS_EXPORT_TS = "-payments-export-ts",
  SENT_JOIN_PRIVATE = "sent-join-private-",
  AUTH_METHOD = "auth-method",
  AUTH_EMAIL = "auth-email",
  LANGUAGE = "language",
  AUTH_TOKEN_NAME = "auth._token.local",
  PREFIX_STORAGE = "experiment_",
  HIDE_UNAVAILABLE_COUNTRIES_UNTIL_MSEC = "cache_until",
  UNPAID_ORDER = "unpaid_order",
  OAUTH_AUTH_ID = "oauth_auth_id",
  EMAIL_VERIFICATION_TOOLTIP = "email_verification_tooltip",
  DISCLOSURE_TEAM = "disclosure_team",
  BANNER = "banner",
  FIRST_LOGIN = "user_first_login",
  USER_WARN_TERMS_APPROVED = "updated_terms_approved",
  NEW_PST_LANDING_EXPERIMENT = "new_pst_landing_experiment",
  NOTIFICATION_BANNERS = "notification_banners",
}
