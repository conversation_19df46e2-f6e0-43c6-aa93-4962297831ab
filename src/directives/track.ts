import { useTracker, TrackerEvent } from "@/composable";
const tracker = useTracker();

const vTrack = {
  created(el: any, binding: any) {
    if (binding.arg === "button") {
      el.addEventListener("click", async () => {
        await tracker.logEvent(TrackerEvent.BUTTON, binding.value);
      });
    }
  },

  mounted(el: any, binding: any) {
    if (binding.arg === "visit") {
      tracker.logEvent(TrackerEvent.VISIT, binding.value);
    }
  },
};

export default vTrack;
