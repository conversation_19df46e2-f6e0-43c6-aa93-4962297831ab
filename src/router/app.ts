import type { RouteRecordRaw } from "vue-router";
import { RouteName } from "@/constants/route_name";
import { useSubscriptionsInfoGet } from "@/composable/API/useSubscriptionsInfoGet";
import { useEmailVerificationProcess } from "@/composable/useEmailVerificationProcess";
import { nonUltimaSubscriptionCardsLimitGuard } from "@/middleware/nonUltimaSubscriptionCardsLimitGuard";

/**
 * Main application routes configuration
 * @description Contains the main application routes that a user sees after logging in.
 * Includes core features like dashboard, cards, settings, accounts, payments
 * @example
 * - /app/dashboard - Main dashboard view
 * - /app/cards - Card management
 * - /app/settings - User settings
 * - /app/accounts - Account management
 * - /app/payments - Payment processing
 * @type RouteRecordRaw[]
 */
const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: () => {
      return "/app/dashboard";
    },
  },
  {
    path: "/app",
    redirect: () => {
      return "/app/dashboard";
    },
  },
  // old routes save
  {
    path: "/faq",
    redirect: () => {
      return "/app/faq";
    },
  },
  {
    path: "/settings",
    redirect: () => {
      return "/app/settings";
    },
  },
  {
    path: "/app/dashboard",
    component: () => import("@/views/Dashboard.new.vue"),
    name: RouteName.DASHBOARD,
  },
  {
    path: "/app/finance",
    component: () => import("@/views/FinanceView.vue"),
    name: RouteName.FINANCE,
  },
  {
    path: "/app/faq",
    component: () => import("@/views/Faq.deprecated.vue"),
    name: RouteName.FAQ,
    children: [
      {
        path: ":category",
        component: () => import("@/components/Faq/index.deprecated.vue"),
        name: RouteName.FAQ_CATEGORY,
      },
    ],
  },
  {
    path: "/app/affiliate",
    component: () => import("@/views/Affiliate.vue"),
    name: RouteName.AFFILIATE,
  },
  {
    path: "/app/rewards",
    component: () => import("@/views/RewardsView.vue"),
    name: RouteName.REWARDS,
  },
  {
    path: "/app/accounts",
    component: () => import("@/views/Accounts.vue"),
    name: RouteName.ACCOUNTS,
  },
  {
    path: "/app/cards",
    component: () => import("@/views/CardsView.vue"),
    // component: () => import("@/views/Cards.vue"),
    name: RouteName.CARDS,
  },
  // {
  //   path: "/app/cards-v2",
  //   component: () => import("@/views/CardsView.vue"),
  //   name: "cards.v2",
  // },
  {
    path: "/app/settings",
    component: () => import("@/views/SettingsView/SettingsView.vue"),
    name: RouteName.SETTINGS,
    redirect: () => "/app/settings/account",
    children: [
      {
        path: "account",
        component: () => import("@/views/SettingsView/SettingsAccountView.vue"),
        name: RouteName.ACCOUNT_SETTINGS,
      },
      {
        path: "verification",
        component: () =>
          import("@/views/VerificationView/VerificationView.vue"),
        name: RouteName.VERIFICATION_SETTINGS,
      },
      {
        path: "notifications",
        component: () =>
          import("@/views/SettingsView/SettingsNotificationsView.vue"),
        name: RouteName.NOTIFICATIONS_SETTINGS,
      },
      {
        path: "sessions",
        component: () =>
          import("@/views/SettingsView/SettingsSessionsView.vue"),
        name: RouteName.SESSIONS_SETTINGS,
      },
      {
        path: "api",
        component: () => import("@/views/SettingsView/SettingsApiView.vue"),
        name: RouteName.API_SETTINGS,
      },
    ],
  },
  {
    path: "/app/team",
    component: () => import("@/views/Team.vue"),
    name: RouteName.TEAM,
  },
  // {
  //   path: "/app/payments",
  //   component: () => import("@/views/Payments.deprecated.vue"),
  //   name: RouteName.PERSONAL_PAYMENTS,
  // },
  {
    path: "/app/payments",
    component: () => import("@/views/PaymentsView.vue"),
    name: RouteName.PERSONAL_PAYMENTS,
  },
  {
    path: "/app/create",
    component: () => import("@/views/CreateCardViewV2.vue"),
    meta: {
      layout: "base",
    },
    beforeEnter: nonUltimaSubscriptionCardsLimitGuard,
    name: RouteName.CREATE_CARD,
  },
  {
    path: "/app/create/:slug",
    component: () => import("@/views/CreateCardExperiment.vue"),
    meta: {
      layout: "base",
    },
    name: RouteName.CREATE_CARD_WITH_SLUG,
  },
  {
    path: "/app/experiments",
    component: () => import("@/views/Experiments.vue"),
    name: RouteName.EXPERIMENTS,
  },
  {
    path: "/app/unavailable-countries",
    component: () => import("@/views/UnavailableCountries.vue"),
    meta: {
      layout: "base",
    },
    name: RouteName.UNAVAILABLE_COUNTRIES,
  },
  {
    path: "/app/feedback-bonus",
    component: () => import("@/views/FeedbackBonus/FeedbackBonusView.vue"),
    name: RouteName.FEEDBACK_BONUS,
  },
  {
    path: "/app/email-verification-process",
    name: RouteName.EMAIL_VERIFICATION_PROCESS,
    beforeEnter: async (to) => {
      const { checkEmailVerificationHash } = useEmailVerificationProcess();
      await checkEmailVerificationHash(to.query.hash as string);
    },
    component: () => import("@/views/EmailVerificationComplete.vue"),
  },
  {
    path: "/app/select-tariff",
    name: RouteName.SELECT_TARIFF,
    component: () => import("@/views/SelectTariffView.vue"),
  },
  {
    path: "/app/email-verification-complete",
    component: () => import("@/views/EmailVerificationComplete.vue"),
    name: RouteName.EMAIL_VERIFICATION_COMPLETE,
  },
  {
    path: "/app/subscription",
    component: () => import("@/views/SubscriptionsView/SubscriptionView.vue"),
    name: RouteName.SUBSCRIPTION,
    redirect: "/app/subscription/detail",
    children: [
      {
        path: "/app/subscription/detail",
        component: () =>
          import("@/views/SubscriptionsView/SubscriptionDetailView.vue"),
        name: RouteName.SUBSCRIPTION_DETAIL,
        beforeEnter: async (to, from, next) => {
          const { data } = await useSubscriptionsInfoGet();
          if (data.value?.data?.status) {
            next();
          } else {
            next("/app/subscription/promo");
          }
        },
      },
      {
        path: "/app/subscription/cashback",
        component: () =>
          import("@/views/SubscriptionsView/SubscriptionCashbackView.vue"),
        name: RouteName.CASHBACK,
      },
      {
        path: "/app/subscription/view-tariffs",
        component: () =>
          import("@/views/SubscriptionsView/SubscriptionTariffsView.vue"),
        name: RouteName.SUBSCRIPTION_TARIFF,
      },
      // {
      //   path: "/app/subscription/missed-cashback",
      //   component: () => import("@/views/MissedCashbackPage.deprecated.vue"),
      //   name: RouteName.MISSED_CASHBACK,
      //   beforeEnter: async (to, from, next) => {
      //     const { data } = await useSubscriptionsInfoGet();
      //     if (data.value?.data?.status) {
      //       next("/app/subscription/cashback");
      //     } else {
      //       next();
      //     }
      //   },
      // },
      {
        path: "/app/subscription/promo",
        component: () =>
          import("@/views/SubscriptionsView/SubscriptionsPromoView.vue"),
        name: RouteName.SUBSCRIPTION_PROMO,
        beforeEnter: async (to, from, next) => {
          const { data } = await useSubscriptionsInfoGet();
          if (data.value?.data?.status) {
            next("/app/subscription/detail");
          } else {
            next();
          }
        },
      },
    ],
  },

  {
    path: "/app/card-operations/:cardId",
    component: () => import("@/views/OperationsView.vue"),
    name: RouteName.OPERATIONS_BY_CARD,
  },
  {
    path: "/app/account-operations/:accountId",
    component: () => import("@/views/OperationsView.vue"),
    name: RouteName.OPERATIONS_BY_ACCOUNT,
  },
  {
    path: "/app/trustpilot-widget",
    component: () => import("@/views/TrustpilotView.vue"),
    name: "trustpilot-widget",
  },
];

export default routes;
